use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use std::str::FromStr;
use uuid::Uuid;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, FromRow)]
pub struct Task {
    pub id: String,
    pub name: String,
    pub target: String,
    pub scan_type: String,
    pub status: TaskStatus,
    pub config: Option<String>,
    pub created_at: DateTime<Utc>,
    pub started_at: Option<DateTime<Utc>>,
    pub completed_at: Option<DateTime<Utc>>,
    pub error_message: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "TEXT")]
pub enum TaskStatus {
    #[sqlx(rename = "pending")]
    Pending,
    #[sqlx(rename = "running")]
    Running,
    #[sqlx(rename = "completed")]
    Completed,
    #[sqlx(rename = "failed")]
    Failed,
    #[sqlx(rename = "cancelled")]
    Cancelled,
}

impl std::fmt::Display for TaskStatus {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            TaskStatus::Pending => write!(f, "pending"),
            TaskStatus::Running => write!(f, "running"),
            TaskStatus::Completed => write!(f, "completed"),
            TaskStatus::Failed => write!(f, "failed"),
            TaskStatus::Cancelled => write!(f, "cancelled"),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ScanType {
    Quick,
    Standard,
    Deep,
    WebFocused,
    Custom,
}

impl std::fmt::Display for ScanType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ScanType::Quick => write!(f, "quick"),
            ScanType::Standard => write!(f, "standard"),
            ScanType::Deep => write!(f, "deep"),
            ScanType::WebFocused => write!(f, "web_focused"),
            ScanType::Custom => write!(f, "custom"),
        }
    }
}

impl FromStr for ScanType {
    type Err = String;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s {
            "quick" => Ok(ScanType::Quick),
            "standard" => Ok(ScanType::Standard),
            "deep" => Ok(ScanType::Deep),
            "web_focused" => Ok(ScanType::WebFocused),
            "custom" => Ok(ScanType::Custom),
            _ => Err(format!("Invalid scan type: {}", s)),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateTaskRequest {
    pub name: String,
    pub target: String,
    pub scan_type: ScanType,
    pub config: Option<ScanConfig>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ScanConfig {
    pub ports: Option<String>,
    pub timeout: Option<u64>,
    pub threads: Option<u16>,
    pub enable_nmap: Option<bool>,
    pub enable_dnsx: Option<bool>,
    pub enable_subfinder: Option<bool>,
    pub enable_httpx: Option<bool>,
    pub enable_crawl4ai: Option<bool>,
    pub enable_nuclei: Option<bool>,
    pub nuclei_templates: Option<Vec<String>>,
    pub user_agent: Option<String>,
    pub proxy: Option<String>,
}

impl Default for ScanConfig {
    fn default() -> Self {
        Self {
            ports: None,
            timeout: Some(300),
            threads: Some(100),
            enable_nmap: Some(true),
            enable_dnsx: Some(true),
            enable_subfinder: Some(true),
            enable_httpx: Some(true),
            enable_crawl4ai: Some(false),
            enable_nuclei: Some(true),
            nuclei_templates: None,
            user_agent: None,
            proxy: None,
        }
    }
}

impl Task {
    pub fn new(request: CreateTaskRequest) -> Self {
        let config_json = request
            .config
            .map(|c| serde_json::to_string(&c).unwrap_or_default());

        Self {
            id: Uuid::new_v4().to_string(),
            name: request.name,
            target: request.target,
            scan_type: request.scan_type.to_string(),
            status: TaskStatus::Pending,
            config: config_json,
            created_at: Utc::now(),
            started_at: None,
            completed_at: None,
            error_message: None,
        }
    }

    pub fn get_config(&self) -> Result<ScanConfig, serde_json::Error> {
        match &self.config {
            Some(config_str) => serde_json::from_str(config_str),
            None => Ok(ScanConfig::default()),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskListResponse {
    pub tasks: Vec<Task>,
    pub total: i64,
    pub page: u32,
    pub per_page: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskQuery {
    pub page: Option<u32>,
    pub per_page: Option<u32>,
    pub status: Option<TaskStatus>,
    pub scan_type: Option<String>,
}
