use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct ScanResult {
    pub id: String,
    pub task_id: String,
    pub tool_name: String,
    pub result_type: String,
    pub data: String,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, FromRow)]
pub struct PortResult {
    pub id: String,
    pub task_id: String,
    pub ip_address: String,
    pub port: i32,
    pub protocol: String,
    pub status: String,
    pub service: Option<String>,
    pub version: Option<String>,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct SubdomainResult {
    pub id: String,
    pub task_id: String,
    pub domain: String,
    pub ip_address: Option<String>,
    pub source: Option<String>,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, FromRow)]
pub struct WebResult {
    pub id: String,
    pub task_id: String,
    pub url: String,
    pub status_code: Option<i32>,
    pub title: Option<String>,
    pub server: Option<String>,
    pub technologies: Option<String>,
    pub content_length: Option<i32>,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct VulnerabilityResult {
    pub id: String,
    pub task_id: String,
    pub template_id: String,
    pub name: String,
    pub severity: String,
    pub url: String,
    pub matched_at: Option<String>,
    pub description: Option<String>,
    pub reference: Option<String>,
    pub created_at: DateTime<Utc>,
}

impl PortResult {
    pub fn new(task_id: &str, ip: &str, port: u16, protocol: &str, status: &str) -> Self {
        Self {
            id: Uuid::new_v4().to_string(),
            task_id: task_id.to_string(),
            ip_address: ip.to_string(),
            port: port as i32,
            protocol: protocol.to_string(),
            status: status.to_string(),
            service: None,
            version: None,
            created_at: Utc::now(),
        }
    }
}

impl SubdomainResult {
    pub fn new(task_id: &str, domain: &str, ip: Option<&str>, source: Option<&str>) -> Self {
        Self {
            id: Uuid::new_v4().to_string(),
            task_id: task_id.to_string(),
            domain: domain.to_string(),
            ip_address: ip.map(|s| s.to_string()),
            source: source.map(|s| s.to_string()),
            created_at: Utc::now(),
        }
    }
}

impl WebResult {
    pub fn new(task_id: &str, url: &str) -> Self {
        Self {
            id: Uuid::new_v4().to_string(),
            task_id: task_id.to_string(),
            url: url.to_string(),
            status_code: None,
            title: None,
            server: None,
            technologies: None,
            content_length: None,
            created_at: Utc::now(),
        }
    }
}

impl VulnerabilityResult {
    pub fn new(task_id: &str, template_id: &str, name: &str, severity: &str, url: &str) -> Self {
        Self {
            id: Uuid::new_v4().to_string(),
            task_id: task_id.to_string(),
            template_id: template_id.to_string(),
            name: name.to_string(),
            severity: severity.to_string(),
            url: url.to_string(),
            matched_at: None,
            description: None,
            reference: None,
            created_at: Utc::now(),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskResults {
    pub task_id: String,
    pub ports: Vec<PortResult>,
    pub subdomains: Vec<SubdomainResult>,
    pub web_assets: Vec<WebResult>,
    pub vulnerabilities: Vec<VulnerabilityResult>,
    pub summary: ResultSummary,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResultSummary {
    pub total_ports: usize,
    pub open_ports: usize,
    pub total_subdomains: usize,
    pub total_web_assets: usize,
    pub total_vulnerabilities: usize,
    pub high_severity_vulns: usize,
    pub medium_severity_vulns: usize,
    pub low_severity_vulns: usize,
}

impl Default for ResultSummary {
    fn default() -> Self {
        Self {
            total_ports: 0,
            open_ports: 0,
            total_subdomains: 0,
            total_web_assets: 0,
            total_vulnerabilities: 0,
            high_severity_vulns: 0,
            medium_severity_vulns: 0,
            low_severity_vulns: 0,
        }
    }
}
