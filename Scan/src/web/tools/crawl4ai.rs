use log::{error, info};
use reqwest::Client;
use serde_json::Value;
use std::time::Duration;

use crate::common::UserAgentGenerator;
use crate::models::{ScanConfig, WebResult};

pub struct Crawl4aiTool {
    client: Client,
    user_agent_generator: UserAgentGenerator,
}

impl Crawl4aiTool {
    pub fn new() -> Self {
        let client = Client::builder()
            .timeout(Duration::from_secs(30))
            .build()
            .unwrap_or_default();

        Self {
            client,
            user_agent_generator: UserAgentGenerator::new(),
        }
    }

    pub async fn crawl_url(
        &self,
        task_id: &str,
        url: &str,
        config: &ScanConfig,
    ) -> Result<WebResult, Box<dyn std::error::Error>> {
        info!("Starting Crawl4AI crawling for URL: {}", url);

        // 构建请求
        let user_agent = config
            .user_agent
            .as_deref()
            .unwrap_or_else(|| self.user_agent_generator.get_random_browser_agent());

        let mut request = self.client.get(url).header("User-Agent", user_agent);

        // 设置超时
        if let Some(timeout) = config.timeout {
            request = request.timeout(Duration::from_secs(timeout));
        }

        // 设置代理
        if let Some(proxy_url) = &config.proxy {
            if let Ok(proxy) = reqwest::Proxy::all(proxy_url) {
                let client = Client::builder()
                    .proxy(proxy)
                    .timeout(Duration::from_secs(config.timeout.unwrap_or(30)))
                    .build()?;
                request = client.get(url).header("User-Agent", user_agent);
            }
        }

        let response = request.send().await?;

        let mut result = WebResult::new(task_id, url);
        result.status_code = Some(response.status().as_u16() as i32);

        // 获取服务器信息
        if let Some(server) = response.headers().get("server") {
            if let Ok(server_str) = server.to_str() {
                result.server = Some(server_str.to_string());
            }
        }

        // 获取内容长度
        if let Some(content_length) = response.headers().get("content-length") {
            if let Ok(length_str) = content_length.to_str() {
                if let Ok(length) = length_str.parse::<i32>() {
                    result.content_length = Some(length);
                }
            }
        }

        // 获取页面内容
        let html = response.text().await?;
        result.content_length = Some(html.len() as i32);

        // 提取标题
        if let Some(title) = self.extract_title(&html) {
            result.title = Some(title);
        }

        // 检测技术栈
        let technologies = self.detect_technologies(&html);
        if !technologies.is_empty() {
            result.technologies = Some(technologies.join(", "));
        }

        info!("Crawl4AI crawling completed for URL: {}", url);
        Ok(result)
    }

    pub async fn crawl_multiple_urls(
        &self,
        task_id: &str,
        urls: &[String],
        config: &ScanConfig,
    ) -> Result<Vec<WebResult>, Box<dyn std::error::Error>> {
        info!("Starting Crawl4AI crawling for {} URLs", urls.len());

        let mut results = Vec::new();
        let max_concurrent = config.threads.unwrap_or(10) as usize;

        // 分批处理URL以控制并发数
        for chunk in urls.chunks(max_concurrent) {
            let mut handles = Vec::new();

            for url in chunk {
                let task_id = task_id.to_string();
                let url = url.clone();
                let config = config.clone();
                let tool = self.clone();

                let handle =
                    tokio::spawn(async move { tool.crawl_url(&task_id, &url, &config).await });

                handles.push(handle);
            }

            // 等待当前批次完成
            for handle in handles {
                match handle.await {
                    Ok(Ok(result)) => results.push(result),
                    Ok(Err(e)) => error!("Crawl failed: {}", e),
                    Err(e) => error!("Task join failed: {}", e),
                }
            }
        }

        info!(
            "Crawl4AI crawling completed, processed {} URLs",
            results.len()
        );
        Ok(results)
    }

    pub async fn extract_links(
        &self,
        url: &str,
        config: &ScanConfig,
    ) -> Result<Vec<String>, Box<dyn std::error::Error>> {
        info!("Extracting links from URL: {}", url);

        let user_agent = config
            .user_agent
            .as_deref()
            .unwrap_or_else(|| self.user_agent_generator.get_random_browser_agent());

        let response = self
            .client
            .get(url)
            .header("User-Agent", user_agent)
            .timeout(Duration::from_secs(config.timeout.unwrap_or(30)))
            .send()
            .await?;

        let html = response.text().await?;
        let links = self.extract_links_from_html(&html, url);

        info!("Extracted {} links from URL: {}", links.len(), url);
        Ok(links)
    }

    fn extract_title(&self, html: &str) -> Option<String> {
        // 简单的标题提取
        if let Some(start) = html.find("<title>") {
            if let Some(end) = html[start + 7..].find("</title>") {
                let title = &html[start + 7..start + 7 + end];
                return Some(title.trim().to_string());
            }
        }
        None
    }

    fn detect_technologies(&self, html: &str) -> Vec<String> {
        let mut technologies = Vec::new();

        // 检测常见的Web技术
        if html.contains("wp-content") || html.contains("wordpress") {
            technologies.push("WordPress".to_string());
        }

        if html.contains("drupal") {
            technologies.push("Drupal".to_string());
        }

        if html.contains("joomla") {
            technologies.push("Joomla".to_string());
        }

        if html.contains("react") || html.contains("React") {
            technologies.push("React".to_string());
        }

        if html.contains("vue") || html.contains("Vue") {
            technologies.push("Vue.js".to_string());
        }

        if html.contains("angular") || html.contains("Angular") {
            technologies.push("Angular".to_string());
        }

        if html.contains("jquery") || html.contains("jQuery") {
            technologies.push("jQuery".to_string());
        }

        if html.contains("bootstrap") || html.contains("Bootstrap") {
            technologies.push("Bootstrap".to_string());
        }

        technologies
    }

    fn extract_links_from_html(&self, html: &str, base_url: &str) -> Vec<String> {
        let mut links = Vec::new();

        // 简单的链接提取（实际应用中应该使用HTML解析器）
        let patterns = [
            r#"href="([^"]+)""#,
            r#"src="([^"]+)""#,
            r#"action="([^"]+)""#,
        ];

        for pattern in &patterns {
            if let Ok(re) = regex::Regex::new(pattern) {
                for cap in re.captures_iter(html) {
                    if let Some(link) = cap.get(1) {
                        let link_str = link.as_str();

                        // 处理相对链接
                        let full_link = if link_str.starts_with("http") {
                            link_str.to_string()
                        } else if link_str.starts_with('/') {
                            format!("{}{}", base_url.trim_end_matches('/'), link_str)
                        } else {
                            format!("{}/{}", base_url.trim_end_matches('/'), link_str)
                        };

                        links.push(full_link);
                    }
                }
            }
        }

        // 去重
        links.sort();
        links.dedup();

        links
    }

    pub fn is_available() -> bool {
        // Crawl4AI是基于HTTP的工具，只要有网络连接就可用
        true
    }
}

impl Clone for Crawl4aiTool {
    fn clone(&self) -> Self {
        Self::new()
    }
}

impl Default for Crawl4aiTool {
    fn default() -> Self {
        Self::new()
    }
}
