use log::{error, info};
use std::process::{Command, Stdio};
use tokio::process::Command as TokioCommand;

use crate::models::{ScanConfig, SubdomainResult};
use crate::tools::ResultAnalyzer;

pub struct DnsxTool {
    analyzer: ResultAnalyzer,
}

impl DnsxTool {
    pub fn new() -> Self {
        Self {
            analyzer: ResultAnalyzer::new(),
        }
    }

    pub async fn resolve_domains(
        &self,
        task_id: &str,
        domains: &[String],
        config: &ScanConfig,
    ) -> Result<Vec<SubdomainResult>, Box<dyn std::error::Error>> {
        info!("Starting DNSx resolution for {} domains", domains.len());

        if domains.is_empty() {
            return Ok(Vec::new());
        }

        // 创建临时文件存储域名列表
        let domain_list = domains.join("\n");

        let mut cmd = TokioCommand::new("dnsx");

        cmd.arg("-silent") // 静默模式
            .arg("-a") // A记录
            .arg("-resp") // 显示响应
            .arg("-json"); // JSON输出

        // 设置超时
        if let Some(timeout) = config.timeout {
            cmd.arg("-timeout").arg(timeout.to_string());
        }

        // 设置线程数
        if let Some(threads) = config.threads {
            cmd.arg("-t").arg(threads.to_string());
        }

        // 通过stdin传递域名列表
        cmd.stdin(Stdio::piped())
            .stdout(Stdio::piped())
            .stderr(Stdio::piped());

        let mut child = cmd.spawn()?;

        // 写入域名列表到stdin
        if let Some(stdin) = child.stdin.take() {
            use tokio::io::AsyncWriteExt;
            let mut stdin = stdin;
            stdin.write_all(domain_list.as_bytes()).await?;
            stdin.shutdown().await?;
        }

        let output = child.wait_with_output().await?;

        if !output.status.success() {
            let error_msg = String::from_utf8_lossy(&output.stderr);
            error!("DNSx resolution failed: {}", error_msg);
            return Err(format!("DNSx resolution failed: {}", error_msg).into());
        }

        let stdout = String::from_utf8_lossy(&output.stdout);
        let results = self.analyzer.parse_dnsx_output(task_id, &stdout);

        info!(
            "DNSx resolution completed, resolved {} domains",
            results.len()
        );
        Ok(results)
    }

    pub async fn reverse_dns(
        &self,
        task_id: &str,
        ips: &[String],
        config: &ScanConfig,
    ) -> Result<Vec<SubdomainResult>, Box<dyn std::error::Error>> {
        info!("Starting DNSx reverse DNS for {} IPs", ips.len());

        if ips.is_empty() {
            return Ok(Vec::new());
        }

        let ip_list = ips.join("\n");

        let mut cmd = TokioCommand::new("dnsx");

        cmd.arg("-silent")
            .arg("-ptr") // PTR记录
            .arg("-resp")
            .arg("-json");

        if let Some(timeout) = config.timeout {
            cmd.arg("-timeout").arg(timeout.to_string());
        }

        if let Some(threads) = config.threads {
            cmd.arg("-t").arg(threads.to_string());
        }

        cmd.stdin(Stdio::piped())
            .stdout(Stdio::piped())
            .stderr(Stdio::piped());

        let mut child = cmd.spawn()?;

        if let Some(stdin) = child.stdin.take() {
            use tokio::io::AsyncWriteExt;
            let mut stdin = stdin;
            stdin.write_all(ip_list.as_bytes()).await?;
            stdin.shutdown().await?;
        }

        let output = child.wait_with_output().await?;

        if !output.status.success() {
            let error_msg = String::from_utf8_lossy(&output.stderr);
            error!("DNSx reverse DNS failed: {}", error_msg);
            return Err(format!("DNSx reverse DNS failed: {}", error_msg).into());
        }

        let stdout = String::from_utf8_lossy(&output.stdout);
        let results = self.analyzer.parse_dnsx_output(task_id, &stdout);

        info!("DNSx reverse DNS completed");
        Ok(results)
    }

    pub fn is_available() -> bool {
        Command::new("dnsx")
            .arg("-version")
            .stdout(Stdio::null())
            .stderr(Stdio::null())
            .status()
            .map(|status| status.success())
            .unwrap_or(false)
    }
}

impl Default for DnsxTool {
    fn default() -> Self {
        Self::new()
    }
}
