use log::{error, info};
use std::process::{Command, Stdio};
use tokio::process::Command as TokioCommand;

use crate::web::models::{PortResult, ScanConfig};
use crate::web::tools::ResultAnalyzer;

pub struct NmapTool {
    analyzer: ResultAnalyzer,
}

impl NmapTool {
    pub fn new() -> Self {
        Self {
            analyzer: ResultAnalyzer::new(),
        }
    }

    pub async fn scan_ports(
        &self,
        task_id: &str,
        target: &str,
        ports: Option<&str>,
        config: &ScanConfig,
    ) -> Result<Vec<PortResult>, Box<dyn std::error::Error>> {
        info!("Starting Nmap port scan for target: {}", target);

        let mut cmd = TokioCommand::new("nmap");

        // 基本参数
        cmd.arg("-sS") // SYN scan
            .arg("-T4") // 时序模板
            .arg("--open") // 只显示开放端口
            .arg("-n"); // 不进行DNS解析

        // 端口范围
        if let Some(port_range) = ports {
            cmd.arg("-p").arg(port_range);
        } else {
            cmd.arg("--top-ports").arg("1000");
        }

        // 超时设置
        if let Some(timeout) = config.timeout {
            cmd.arg("--host-timeout").arg(format!("{}s", timeout));
        }

        // 线程数
        if let Some(threads) = config.threads {
            cmd.arg("--min-parallelism").arg(threads.to_string());
        }

        // 目标
        cmd.arg(target);

        // 执行命令
        let output = cmd.output().await?;

        if !output.status.success() {
            let error_msg = String::from_utf8_lossy(&output.stderr);
            error!("Nmap scan failed: {}", error_msg);
            return Err(format!("Nmap scan failed: {}", error_msg).into());
        }

        let stdout = String::from_utf8_lossy(&output.stdout);
        let results = self.analyzer.parse_nmap_output(task_id, &stdout);

        info!("Nmap scan completed, found {} open ports", results.len());
        Ok(results)
    }

    pub async fn service_detection(
        &self,
        task_id: &str,
        target: &str,
        ports: &[u16],
        config: &ScanConfig,
    ) -> Result<Vec<PortResult>, Box<dyn std::error::Error>> {
        info!("Starting Nmap service detection for target: {}", target);

        if ports.is_empty() {
            return Ok(Vec::new());
        }

        let port_list = ports
            .iter()
            .map(|p| p.to_string())
            .collect::<Vec<_>>()
            .join(",");

        let mut cmd = TokioCommand::new("nmap");

        cmd.arg("-sV") // 版本检测
            .arg("-sC") // 默认脚本
            .arg("-T4")
            .arg("-n")
            .arg("-p")
            .arg(&port_list)
            .arg(target);

        let output = cmd.output().await?;

        if !output.status.success() {
            let error_msg = String::from_utf8_lossy(&output.stderr);
            error!("Nmap service detection failed: {}", error_msg);
            return Err(format!("Nmap service detection failed: {}", error_msg).into());
        }

        let stdout = String::from_utf8_lossy(&output.stdout);
        let results = self.analyzer.parse_nmap_output(task_id, &stdout);

        info!("Nmap service detection completed");
        Ok(results)
    }

    pub fn is_available() -> bool {
        Command::new("nmap")
            .arg("--version")
            .stdout(Stdio::null())
            .stderr(Stdio::null())
            .status()
            .map(|status| status.success())
            .unwrap_or(false)
    }
}

impl Default for NmapTool {
    fn default() -> Self {
        Self::new()
    }
}
