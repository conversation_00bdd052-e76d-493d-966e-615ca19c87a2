use log::{error, info};
use serde_json::Value;

use crate::web::models::{PortResult, SubdomainResult, VulnerabilityResult, WebResult};

pub struct ResultAnalyzer;

impl ResultAnalyzer {
    pub fn new() -> Self {
        Self
    }

    pub fn parse_nmap_output(&self, task_id: &str, output: &str) -> Vec<PortResult> {
        let mut results = Vec::new();

        // 简化的Nmap输出解析
        // 实际实现应该使用更复杂的解析逻辑或XML输出
        for line in output.lines() {
            if line.contains("/tcp") || line.contains("/udp") {
                if let Some(port_result) = self.parse_nmap_port_line(task_id, line) {
                    results.push(port_result);
                }
            }
        }

        info!("Parsed {} port results from Nmap output", results.len());
        results
    }

    pub fn parse_subfinder_output(&self, task_id: &str, output: &str) -> Vec<SubdomainResult> {
        let mut results = Vec::new();

        for line in output.lines() {
            let line = line.trim();
            if !line.is_empty() && line.contains('.') {
                let subdomain = SubdomainResult::new(task_id, line, None, Some("subfinder"));
                results.push(subdomain);
            }
        }

        info!(
            "Parsed {} subdomain results from Subfinder output",
            results.len()
        );
        results
    }

    pub fn parse_httpx_output(&self, task_id: &str, output: &str) -> Vec<WebResult> {
        let mut results = Vec::new();

        for line in output.lines() {
            let line = line.trim();
            if line.starts_with("http://") || line.starts_with("https://") {
                if let Some(web_result) = self.parse_httpx_line(task_id, line) {
                    results.push(web_result);
                }
            }
        }

        info!("Parsed {} web results from HTTPx output", results.len());
        results
    }

    pub fn parse_nuclei_output(&self, task_id: &str, output: &str) -> Vec<VulnerabilityResult> {
        let mut results = Vec::new();

        // 尝试解析JSON格式的Nuclei输出
        for line in output.lines() {
            let line = line.trim();
            if line.starts_with('{') && line.ends_with('}') {
                if let Ok(json) = serde_json::from_str::<Value>(line) {
                    if let Some(vuln_result) = self.parse_nuclei_json(task_id, &json) {
                        results.push(vuln_result);
                    }
                }
            }
        }

        info!(
            "Parsed {} vulnerability results from Nuclei output",
            results.len()
        );
        results
    }

    pub fn parse_dnsx_output(&self, task_id: &str, output: &str) -> Vec<SubdomainResult> {
        let mut results = Vec::new();

        for line in output.lines() {
            let line = line.trim();
            if let Some((domain, ip)) = self.parse_dnsx_line(line) {
                let subdomain = SubdomainResult::new(task_id, &domain, Some(&ip), Some("dnsx"));
                results.push(subdomain);
            }
        }

        info!("Parsed {} DNS results from DNSx output", results.len());
        results
    }

    // 私有辅助方法
    fn parse_nmap_port_line(&self, task_id: &str, line: &str) -> Option<PortResult> {
        // 解析类似 "22/tcp open ssh OpenSSH 7.4" 的行
        let parts: Vec<&str> = line.split_whitespace().collect();
        if parts.len() < 3 {
            return None;
        }

        let port_protocol = parts[0];
        let status = parts[1];
        let service = if parts.len() > 2 {
            Some(parts[2].to_string())
        } else {
            None
        };
        let version = if parts.len() > 3 {
            Some(parts[3..].join(" "))
        } else {
            None
        };

        // 解析端口和协议
        let port_parts: Vec<&str> = port_protocol.split('/').collect();
        if port_parts.len() != 2 {
            return None;
        }

        let port: u16 = port_parts[0].parse().ok()?;
        let protocol = port_parts[1];

        let mut result = PortResult::new(task_id, "127.0.0.1", port, protocol, status);
        result.service = service;
        result.version = version;

        Some(result)
    }

    fn parse_httpx_line(&self, task_id: &str, line: &str) -> Option<WebResult> {
        // 解析类似 "https://example.com [200] [Apache/2.4.41] [Example Site]" 的行
        let parts: Vec<&str> = line.split_whitespace().collect();
        if parts.is_empty() {
            return None;
        }

        let url = parts[0];
        let mut result = WebResult::new(task_id, url);

        // 尝试解析状态码
        for part in &parts[1..] {
            if part.starts_with('[') && part.ends_with(']') {
                let content = &part[1..part.len() - 1];
                if let Ok(status_code) = content.parse::<i32>() {
                    result.status_code = Some(status_code);
                } else if content.contains('/') {
                    result.server = Some(content.to_string());
                } else {
                    result.title = Some(content.to_string());
                }
            }
        }

        Some(result)
    }

    fn parse_nuclei_json(&self, task_id: &str, json: &Value) -> Option<VulnerabilityResult> {
        let template_id = json.get("template-id")?.as_str()?;
        let name = json.get("info")?.get("name")?.as_str()?;
        let severity = json.get("info")?.get("severity")?.as_str()?;
        let matched_at = json.get("matched-at")?.as_str()?;

        let description = json
            .get("info")?
            .get("description")?
            .as_str()
            .map(|s| s.to_string());
        let reference = json
            .get("info")?
            .get("reference")?
            .as_array()
            .and_then(|arr| arr.first())
            .and_then(|v| v.as_str())
            .map(|s| s.to_string());

        let mut result = VulnerabilityResult::new(task_id, template_id, name, severity, matched_at);
        result.description = description;
        result.reference = reference;

        Some(result)
    }

    fn parse_dnsx_line(&self, line: &str) -> Option<(String, String)> {
        // 解析类似 "example.com [***********]" 的行
        if let Some(bracket_start) = line.find('[') {
            if let Some(bracket_end) = line.find(']') {
                let domain = line[..bracket_start].trim().to_string();
                let ip = line[bracket_start + 1..bracket_end].trim().to_string();
                return Some((domain, ip));
            }
        }

        None
    }

    pub fn calculate_risk_score(&self, vulnerabilities: &[VulnerabilityResult]) -> f32 {
        let mut score = 0.0;

        for vuln in vulnerabilities {
            match vuln.severity.to_lowercase().as_str() {
                "critical" => score += 10.0,
                "high" => score += 7.0,
                "medium" => score += 4.0,
                "low" => score += 1.0,
                "info" => score += 0.1,
                _ => score += 0.0,
            }
        }

        // 归一化到0-100范围
        (score / (vulnerabilities.len() as f32 * 10.0) * 100.0).min(100.0)
    }

    pub fn generate_recommendations(&self, vulnerabilities: &[VulnerabilityResult]) -> Vec<String> {
        let mut recommendations = Vec::new();

        let critical_count = vulnerabilities
            .iter()
            .filter(|v| v.severity == "critical")
            .count();
        let high_count = vulnerabilities
            .iter()
            .filter(|v| v.severity == "high")
            .count();
        let medium_count = vulnerabilities
            .iter()
            .filter(|v| v.severity == "medium")
            .count();

        if critical_count > 0 {
            recommendations.push(format!(
                "立即修复 {} 个严重漏洞，这些漏洞可能导致系统完全被攻破",
                critical_count
            ));
        }

        if high_count > 0 {
            recommendations.push(format!(
                "优先修复 {} 个高危漏洞，建议在24小时内完成",
                high_count
            ));
        }

        if medium_count > 0 {
            recommendations.push(format!(
                "计划修复 {} 个中危漏洞，建议在一周内完成",
                medium_count
            ));
        }

        if vulnerabilities.is_empty() {
            recommendations.push("未发现明显的安全漏洞，但建议定期进行安全扫描".to_string());
        }

        recommendations
    }
}
