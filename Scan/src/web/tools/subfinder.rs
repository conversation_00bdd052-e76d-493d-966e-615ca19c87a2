use log::{error, info};
use std::process::{Command, Stdio};
use tokio::process::Command as TokioCommand;

use crate::models::{ScanConfig, SubdomainResult};
use crate::tools::ResultAnalyzer;

pub struct SubfinderTool {
    analyzer: ResultAnalyzer,
}

impl SubfinderTool {
    pub fn new() -> Self {
        Self {
            analyzer: ResultAnalyzer::new(),
        }
    }

    pub async fn enumerate_subdomains(
        &self,
        task_id: &str,
        domain: &str,
        config: &ScanConfig,
    ) -> Result<Vec<SubdomainResult>, Box<dyn std::error::Error>> {
        info!("Starting Subfinder enumeration for domain: {}", domain);

        let mut cmd = TokioCommand::new("subfinder");

        cmd.arg("-d")
            .arg(domain) // 目标域名
            .arg("-silent") // 静默模式
            .arg("-all") // 使用所有数据源
            .arg("-recursive"); // 递归查找

        // 设置超时
        if let Some(timeout) = config.timeout {
            cmd.arg("-timeout").arg(timeout.to_string());
        }

        // 设置线程数
        if let Some(threads) = config.threads {
            cmd.arg("-t").arg(threads.to_string());
        }

        let output = cmd.output().await?;

        if !output.status.success() {
            let error_msg = String::from_utf8_lossy(&output.stderr);
            error!("Subfinder enumeration failed: {}", error_msg);
            return Err(format!("Subfinder enumeration failed: {}", error_msg).into());
        }

        let stdout = String::from_utf8_lossy(&output.stdout);
        let results = self.analyzer.parse_subfinder_output(task_id, &stdout);

        info!(
            "Subfinder enumeration completed, found {} subdomains",
            results.len()
        );
        Ok(results)
    }

    pub async fn enumerate_with_sources(
        &self,
        task_id: &str,
        domain: &str,
        sources: &[String],
        config: &ScanConfig,
    ) -> Result<Vec<SubdomainResult>, Box<dyn std::error::Error>> {
        info!(
            "Starting Subfinder enumeration with specific sources for domain: {}",
            domain
        );

        let mut cmd = TokioCommand::new("subfinder");

        cmd.arg("-d").arg(domain).arg("-silent");

        // 指定数据源
        if !sources.is_empty() {
            let sources_str = sources.join(",");
            cmd.arg("-sources").arg(sources_str);
        }

        if let Some(timeout) = config.timeout {
            cmd.arg("-timeout").arg(timeout.to_string());
        }

        if let Some(threads) = config.threads {
            cmd.arg("-t").arg(threads.to_string());
        }

        let output = cmd.output().await?;

        if !output.status.success() {
            let error_msg = String::from_utf8_lossy(&output.stderr);
            error!("Subfinder enumeration failed: {}", error_msg);
            return Err(format!("Subfinder enumeration failed: {}", error_msg).into());
        }

        let stdout = String::from_utf8_lossy(&output.stdout);
        let results = self.analyzer.parse_subfinder_output(task_id, &stdout);

        info!(
            "Subfinder enumeration with sources completed, found {} subdomains",
            results.len()
        );
        Ok(results)
    }

    pub async fn passive_enumeration(
        &self,
        task_id: &str,
        domain: &str,
        config: &ScanConfig,
    ) -> Result<Vec<SubdomainResult>, Box<dyn std::error::Error>> {
        info!(
            "Starting Subfinder passive enumeration for domain: {}",
            domain
        );

        let mut cmd = TokioCommand::new("subfinder");

        cmd.arg("-d").arg(domain).arg("-silent").arg("-passive"); // 仅被动收集

        if let Some(timeout) = config.timeout {
            cmd.arg("-timeout").arg(timeout.to_string());
        }

        if let Some(threads) = config.threads {
            cmd.arg("-t").arg(threads.to_string());
        }

        let output = cmd.output().await?;

        if !output.status.success() {
            let error_msg = String::from_utf8_lossy(&output.stderr);
            error!("Subfinder passive enumeration failed: {}", error_msg);
            return Err(format!("Subfinder passive enumeration failed: {}", error_msg).into());
        }

        let stdout = String::from_utf8_lossy(&output.stdout);
        let results = self.analyzer.parse_subfinder_output(task_id, &stdout);

        info!(
            "Subfinder passive enumeration completed, found {} subdomains",
            results.len()
        );
        Ok(results)
    }

    pub fn get_available_sources() -> Vec<String> {
        // 常见的Subfinder数据源
        vec![
            "alienvault".to_string(),
            "anubis".to_string(),
            "bevigil".to_string(),
            "binaryedge".to_string(),
            "bufferover".to_string(),
            "censys".to_string(),
            "certspotter".to_string(),
            "chaos".to_string(),
            "chinaz".to_string(),
            "crtsh".to_string(),
            "dnsdb".to_string(),
            "fofa".to_string(),
            "fullhunt".to_string(),
            "github".to_string(),
            "hackertarget".to_string(),
            "hunter".to_string(),
            "intelx".to_string(),
            "passivetotal".to_string(),
            "quake".to_string(),
            "rapiddns".to_string(),
            "redhuntlabs".to_string(),
            "robtex".to_string(),
            "securitytrails".to_string(),
            "shodan".to_string(),
            "spyse".to_string(),
            "sublist3r".to_string(),
            "threatbook".to_string(),
            "threatminer".to_string(),
            "virustotal".to_string(),
            "waybackarchive".to_string(),
            "whoisxmlapi".to_string(),
            "zoomeye".to_string(),
        ]
    }

    pub fn is_available() -> bool {
        Command::new("subfinder")
            .arg("-version")
            .stdout(Stdio::null())
            .stderr(Stdio::null())
            .status()
            .map(|status| status.success())
            .unwrap_or(false)
    }
}

impl Default for SubfinderTool {
    fn default() -> Self {
        Self::new()
    }
}
