use log::{error, info};
use std::process::{Command, Stdio};
use tokio::process::Command as TokioCommand;

use crate::common::UserAgentGenerator;
use crate::models::{ScanConfig, VulnerabilityResult};
use crate::tools::ResultAnalyzer;

pub struct NucleiTool {
    analyzer: ResultAnalyzer,
    user_agent_generator: UserAgentGenerator,
}

impl NucleiTool {
    pub fn new() -> Self {
        Self {
            analyzer: ResultAnalyzer::new(),
            user_agent_generator: UserAgentGenerator::new(),
        }
    }

    pub async fn scan_vulnerabilities(
        &self,
        task_id: &str,
        targets: &[String],
        config: &ScanConfig,
    ) -> Result<Vec<VulnerabilityResult>, Box<dyn std::error::Error>> {
        info!(
            "Starting Nuclei vulnerability scan for {} targets",
            targets.len()
        );

        if targets.is_empty() {
            return Ok(Vec::new());
        }

        let target_list = targets.join("\n");

        let mut cmd = TokioCommand::new("nuclei");

        cmd.arg("-silent") // 静默模式
            .arg("-json") // JSON输出
            .arg("-stats") // 显示统计信息
            .arg("-no-color"); // 禁用颜色输出

        // 设置模板
        if let Some(templates) = &config.nuclei_templates {
            if !templates.is_empty() {
                let template_list = templates.join(",");
                cmd.arg("-t").arg(template_list);
            }
        } else {
            // 使用默认模板
            cmd.arg("-t").arg("~/nuclei-templates/");
        }

        // 设置严重程度过滤
        cmd.arg("-severity").arg("critical,high,medium,low");

        // 设置User-Agent
        let user_agent = config
            .user_agent
            .as_deref()
            .unwrap_or_else(|| self.user_agent_generator.get_random_browser_agent());
        cmd.arg("-H").arg(format!("User-Agent: {}", user_agent));

        // 设置超时
        if let Some(timeout) = config.timeout {
            cmd.arg("-timeout").arg(timeout.to_string());
        }

        // 设置并发数
        if let Some(threads) = config.threads {
            cmd.arg("-c").arg(threads.to_string());
        }

        // 设置代理
        if let Some(proxy) = &config.proxy {
            cmd.arg("-proxy").arg(proxy);
        }

        cmd.stdin(Stdio::piped())
            .stdout(Stdio::piped())
            .stderr(Stdio::piped());

        let mut child = cmd.spawn()?;

        // 写入目标列表到stdin
        if let Some(stdin) = child.stdin.take() {
            use tokio::io::AsyncWriteExt;
            let mut stdin = stdin;
            stdin.write_all(target_list.as_bytes()).await?;
            stdin.shutdown().await?;
        }

        let output = child.wait_with_output().await?;

        if !output.status.success() {
            let error_msg = String::from_utf8_lossy(&output.stderr);
            error!("Nuclei vulnerability scan failed: {}", error_msg);
            return Err(format!("Nuclei vulnerability scan failed: {}", error_msg).into());
        }

        let stdout = String::from_utf8_lossy(&output.stdout);
        let results = self.analyzer.parse_nuclei_output(task_id, &stdout);

        info!(
            "Nuclei vulnerability scan completed, found {} vulnerabilities",
            results.len()
        );
        Ok(results)
    }

    pub async fn scan_with_custom_templates(
        &self,
        task_id: &str,
        targets: &[String],
        template_paths: &[String],
        config: &ScanConfig,
    ) -> Result<Vec<VulnerabilityResult>, Box<dyn std::error::Error>> {
        info!(
            "Starting Nuclei scan with custom templates for {} targets",
            targets.len()
        );

        if targets.is_empty() || template_paths.is_empty() {
            return Ok(Vec::new());
        }

        let target_list = targets.join("\n");
        let template_list = template_paths.join(",");

        let mut cmd = TokioCommand::new("nuclei");

        cmd.arg("-silent")
            .arg("-json")
            .arg("-stats")
            .arg("-no-color")
            .arg("-t")
            .arg(template_list);

        let user_agent = config
            .user_agent
            .as_deref()
            .unwrap_or_else(|| self.user_agent_generator.get_random_browser_agent());
        cmd.arg("-H").arg(format!("User-Agent: {}", user_agent));

        if let Some(timeout) = config.timeout {
            cmd.arg("-timeout").arg(timeout.to_string());
        }

        if let Some(threads) = config.threads {
            cmd.arg("-c").arg(threads.to_string());
        }

        if let Some(proxy) = &config.proxy {
            cmd.arg("-proxy").arg(proxy);
        }

        cmd.stdin(Stdio::piped())
            .stdout(Stdio::piped())
            .stderr(Stdio::piped());

        let mut child = cmd.spawn()?;

        if let Some(stdin) = child.stdin.take() {
            use tokio::io::AsyncWriteExt;
            let mut stdin = stdin;
            stdin.write_all(target_list.as_bytes()).await?;
            stdin.shutdown().await?;
        }

        let output = child.wait_with_output().await?;

        if !output.status.success() {
            let error_msg = String::from_utf8_lossy(&output.stderr);
            error!("Nuclei custom template scan failed: {}", error_msg);
            return Err(format!("Nuclei custom template scan failed: {}", error_msg).into());
        }

        let stdout = String::from_utf8_lossy(&output.stdout);
        let results = self.analyzer.parse_nuclei_output(task_id, &stdout);

        info!(
            "Nuclei custom template scan completed, found {} vulnerabilities",
            results.len()
        );
        Ok(results)
    }

    pub async fn scan_by_severity(
        &self,
        task_id: &str,
        targets: &[String],
        severity: &str,
        config: &ScanConfig,
    ) -> Result<Vec<VulnerabilityResult>, Box<dyn std::error::Error>> {
        info!(
            "Starting Nuclei {} severity scan for {} targets",
            severity,
            targets.len()
        );

        if targets.is_empty() {
            return Ok(Vec::new());
        }

        let target_list = targets.join("\n");

        let mut cmd = TokioCommand::new("nuclei");

        cmd.arg("-silent")
            .arg("-json")
            .arg("-stats")
            .arg("-no-color")
            .arg("-severity")
            .arg(severity)
            .arg("-t")
            .arg("~/nuclei-templates/");

        let user_agent = config
            .user_agent
            .as_deref()
            .unwrap_or_else(|| self.user_agent_generator.get_random_browser_agent());
        cmd.arg("-H").arg(format!("User-Agent: {}", user_agent));

        if let Some(timeout) = config.timeout {
            cmd.arg("-timeout").arg(timeout.to_string());
        }

        if let Some(threads) = config.threads {
            cmd.arg("-c").arg(threads.to_string());
        }

        if let Some(proxy) = &config.proxy {
            cmd.arg("-proxy").arg(proxy);
        }

        cmd.stdin(Stdio::piped())
            .stdout(Stdio::piped())
            .stderr(Stdio::piped());

        let mut child = cmd.spawn()?;

        if let Some(stdin) = child.stdin.take() {
            use tokio::io::AsyncWriteExt;
            let mut stdin = stdin;
            stdin.write_all(target_list.as_bytes()).await?;
            stdin.shutdown().await?;
        }

        let output = child.wait_with_output().await?;

        if !output.status.success() {
            let error_msg = String::from_utf8_lossy(&output.stderr);
            error!("Nuclei severity scan failed: {}", error_msg);
            return Err(format!("Nuclei severity scan failed: {}", error_msg).into());
        }

        let stdout = String::from_utf8_lossy(&output.stdout);
        let results = self.analyzer.parse_nuclei_output(task_id, &stdout);

        info!(
            "Nuclei {} severity scan completed, found {} vulnerabilities",
            severity,
            results.len()
        );
        Ok(results)
    }

    pub async fn update_templates() -> Result<(), Box<dyn std::error::Error>> {
        info!("Updating Nuclei templates");

        let output = TokioCommand::new("nuclei")
            .arg("-update-templates")
            .arg("-silent")
            .output()
            .await?;

        if !output.status.success() {
            let error_msg = String::from_utf8_lossy(&output.stderr);
            error!("Nuclei template update failed: {}", error_msg);
            return Err(format!("Nuclei template update failed: {}", error_msg).into());
        }

        info!("Nuclei templates updated successfully");
        Ok(())
    }

    pub fn get_available_severities() -> Vec<String> {
        vec![
            "critical".to_string(),
            "high".to_string(),
            "medium".to_string(),
            "low".to_string(),
            "info".to_string(),
        ]
    }

    pub fn get_template_categories() -> Vec<String> {
        vec![
            "cves".to_string(),
            "vulnerabilities".to_string(),
            "exposures".to_string(),
            "misconfiguration".to_string(),
            "workflows".to_string(),
            "technologies".to_string(),
            "fuzzing".to_string(),
            "default-logins".to_string(),
            "file".to_string(),
            "network".to_string(),
            "dns".to_string(),
            "headless".to_string(),
        ]
    }

    pub fn is_available() -> bool {
        Command::new("nuclei")
            .arg("-version")
            .stdout(Stdio::null())
            .stderr(Stdio::null())
            .status()
            .map(|status| status.success())
            .unwrap_or(false)
    }
}

impl Default for NucleiTool {
    fn default() -> Self {
        Self::new()
    }
}
