use log::{error, info};
use std::process::{Command, Stdio};
use tokio::process::Command as TokioCommand;

use crate::common::UserAgentGenerator;
use crate::models::{ScanConfig, WebResult};
use crate::tools::ResultAnalyzer;

pub struct HttpxTool {
    analyzer: ResultAnalyzer,
    user_agent_generator: UserAgentGenerator,
}

impl HttpxTool {
    pub fn new() -> Self {
        Self {
            analyzer: ResultAnalyzer::new(),
            user_agent_generator: UserAgentGenerator::new(),
        }
    }

    pub async fn probe_urls(
        &self,
        task_id: &str,
        urls: &[String],
        config: &ScanConfig,
    ) -> Result<Vec<WebResult>, Box<dyn std::error::Error>> {
        info!("Starting HTTPx probing for {} URLs", urls.len());

        if urls.is_empty() {
            return Ok(Vec::new());
        }

        let url_list = urls.join("\n");

        let mut cmd = TokioCommand::new("httpx");

        cmd.arg("-silent") // 静默模式
            .arg("-status-code") // 显示状态码
            .arg("-title") // 显示标题
            .arg("-server") // 显示服务器信息
            .arg("-content-length") // 显示内容长度
            .arg("-follow-redirects") // 跟随重定向
            .arg("-json"); // JSON输出

        // 设置User-Agent
        let user_agent = config
            .user_agent
            .as_deref()
            .unwrap_or_else(|| self.user_agent_generator.get_random_browser_agent());
        cmd.arg("-H").arg(format!("User-Agent: {}", user_agent));

        // 设置超时
        if let Some(timeout) = config.timeout {
            cmd.arg("-timeout").arg(timeout.to_string());
        }

        // 设置线程数
        if let Some(threads) = config.threads {
            cmd.arg("-threads").arg(threads.to_string());
        }

        // 设置代理
        if let Some(proxy) = &config.proxy {
            cmd.arg("-http-proxy").arg(proxy);
        }

        cmd.stdin(Stdio::piped())
            .stdout(Stdio::piped())
            .stderr(Stdio::piped());

        let mut child = cmd.spawn()?;

        // 写入URL列表到stdin
        if let Some(stdin) = child.stdin.take() {
            use tokio::io::AsyncWriteExt;
            let mut stdin = stdin;
            stdin.write_all(url_list.as_bytes()).await?;
            stdin.shutdown().await?;
        }

        let output = child.wait_with_output().await?;

        if !output.status.success() {
            let error_msg = String::from_utf8_lossy(&output.stderr);
            error!("HTTPx probing failed: {}", error_msg);
            return Err(format!("HTTPx probing failed: {}", error_msg).into());
        }

        let stdout = String::from_utf8_lossy(&output.stdout);
        let results = self.analyzer.parse_httpx_output(task_id, &stdout);

        info!("HTTPx probing completed, found {} live URLs", results.len());
        Ok(results)
    }

    pub async fn probe_domains(
        &self,
        task_id: &str,
        domains: &[String],
        config: &ScanConfig,
    ) -> Result<Vec<WebResult>, Box<dyn std::error::Error>> {
        info!(
            "Starting HTTPx domain probing for {} domains",
            domains.len()
        );

        if domains.is_empty() {
            return Ok(Vec::new());
        }

        let domain_list = domains.join("\n");

        let mut cmd = TokioCommand::new("httpx");

        cmd.arg("-silent")
            .arg("-status-code")
            .arg("-title")
            .arg("-server")
            .arg("-content-length")
            .arg("-follow-redirects")
            .arg("-ports")
            .arg("80,443,8080,8443,3000,5000,8000,9000") // 常见Web端口
            .arg("-json");

        let user_agent = config
            .user_agent
            .as_deref()
            .unwrap_or_else(|| self.user_agent_generator.get_random_browser_agent());
        cmd.arg("-H").arg(format!("User-Agent: {}", user_agent));

        if let Some(timeout) = config.timeout {
            cmd.arg("-timeout").arg(timeout.to_string());
        }

        if let Some(threads) = config.threads {
            cmd.arg("-threads").arg(threads.to_string());
        }

        if let Some(proxy) = &config.proxy {
            cmd.arg("-http-proxy").arg(proxy);
        }

        cmd.stdin(Stdio::piped())
            .stdout(Stdio::piped())
            .stderr(Stdio::piped());

        let mut child = cmd.spawn()?;

        if let Some(stdin) = child.stdin.take() {
            use tokio::io::AsyncWriteExt;
            let mut stdin = stdin;
            stdin.write_all(domain_list.as_bytes()).await?;
            stdin.shutdown().await?;
        }

        let output = child.wait_with_output().await?;

        if !output.status.success() {
            let error_msg = String::from_utf8_lossy(&output.stderr);
            error!("HTTPx domain probing failed: {}", error_msg);
            return Err(format!("HTTPx domain probing failed: {}", error_msg).into());
        }

        let stdout = String::from_utf8_lossy(&output.stdout);
        let results = self.analyzer.parse_httpx_output(task_id, &stdout);

        info!(
            "HTTPx domain probing completed, found {} live web services",
            results.len()
        );
        Ok(results)
    }

    pub async fn technology_detection(
        &self,
        task_id: &str,
        urls: &[String],
        config: &ScanConfig,
    ) -> Result<Vec<WebResult>, Box<dyn std::error::Error>> {
        info!(
            "Starting HTTPx technology detection for {} URLs",
            urls.len()
        );

        if urls.is_empty() {
            return Ok(Vec::new());
        }

        let url_list = urls.join("\n");

        let mut cmd = TokioCommand::new("httpx");

        cmd.arg("-silent")
            .arg("-status-code")
            .arg("-title")
            .arg("-server")
            .arg("-tech-detect") // 技术检测
            .arg("-content-length")
            .arg("-json");

        let user_agent = config
            .user_agent
            .as_deref()
            .unwrap_or_else(|| self.user_agent_generator.get_random_browser_agent());
        cmd.arg("-H").arg(format!("User-Agent: {}", user_agent));

        if let Some(timeout) = config.timeout {
            cmd.arg("-timeout").arg(timeout.to_string());
        }

        if let Some(threads) = config.threads {
            cmd.arg("-threads").arg(threads.to_string());
        }

        if let Some(proxy) = &config.proxy {
            cmd.arg("-http-proxy").arg(proxy);
        }

        cmd.stdin(Stdio::piped())
            .stdout(Stdio::piped())
            .stderr(Stdio::piped());

        let mut child = cmd.spawn()?;

        if let Some(stdin) = child.stdin.take() {
            use tokio::io::AsyncWriteExt;
            let mut stdin = stdin;
            stdin.write_all(url_list.as_bytes()).await?;
            stdin.shutdown().await?;
        }

        let output = child.wait_with_output().await?;

        if !output.status.success() {
            let error_msg = String::from_utf8_lossy(&output.stderr);
            error!("HTTPx technology detection failed: {}", error_msg);
            return Err(format!("HTTPx technology detection failed: {}", error_msg).into());
        }

        let stdout = String::from_utf8_lossy(&output.stdout);
        let results = self.analyzer.parse_httpx_output(task_id, &stdout);

        info!("HTTPx technology detection completed");
        Ok(results)
    }

    pub fn is_available() -> bool {
        Command::new("httpx")
            .arg("-version")
            .stdout(Stdio::null())
            .stderr(Stdio::null())
            .status()
            .map(|status| status.success())
            .unwrap_or(false)
    }
}

impl Default for HttpxTool {
    fn default() -> Self {
        Self::new()
    }
}
