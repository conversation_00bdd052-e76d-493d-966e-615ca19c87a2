use serde::{Deserialize, Serialize};
use std::env;

#[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct AppConfig {
    pub host: String,
    pub port: u16,
    pub database_url: String,
    pub max_concurrent_tasks: usize,
    pub task_timeout_seconds: u64,
    pub enable_websocket: bool,
}

impl Default for AppConfig {
    fn default() -> Self {
        Self {
            host: "127.0.0.1".to_string(),
            port: 8080,
            database_url: "sqlite:./rustscan.db".to_string(),
            max_concurrent_tasks: 10,
            task_timeout_seconds: 3600, // 1 hour
            enable_websocket: true,
        }
    }
}

impl AppConfig {
    pub fn load() -> Result<Self, Box<dyn std::error::Error>> {
        let mut config = Self::default();

        // 从环境变量加载配置
        if let Ok(host) = env::var("RUSTSCAN_HOST") {
            config.host = host;
        }

        if let Ok(port) = env::var("RUSTSCAN_PORT") {
            config.port = port.parse()?;
        }

        if let Ok(db_url) = env::var("RUSTSCAN_DATABASE_URL") {
            config.database_url = db_url;
        }

        if let Ok(max_tasks) = env::var("RUSTSCAN_MAX_CONCURRENT_TASKS") {
            config.max_concurrent_tasks = max_tasks.parse()?;
        }

        if let Ok(timeout) = env::var("RUSTSCAN_TASK_TIMEOUT") {
            config.task_timeout_seconds = timeout.parse()?;
        }

        if let Ok(ws_enabled) = env::var("RUSTSCAN_ENABLE_WEBSOCKET") {
            config.enable_websocket = ws_enabled.parse().unwrap_or(true);
        }

        Ok(config)
    }
}
