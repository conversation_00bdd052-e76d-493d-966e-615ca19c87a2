use rand::seq::SliceRandom;
use rand::rng;

pub struct UserAgentGenerator {
    browser_agents: Vec<&'static str>,
    mobile_agents: Vec<&'static str>,
    crawler_agents: Vec<&'static str>,
    api_agents: Vec<&'static str>,
}

impl UserAgentGenerator {
    pub fn new() -> Self {
        Self {
            browser_agents: vec![
                // Chrome
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                
                // Firefox
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:121.0) Gecko/20100101 Firefox/121.0",
                "Mozilla/5.0 (X11; Linux x86_64; rv:121.0) Gecko/20100101 Firefox/121.0",
                
                // Safari
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Safari/605.1.15",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Safari/605.1.15",
                
                // Edge
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",
            ],
            
            mobile_agents: vec![
                // iPhone
                "Mozilla/5.0 (iPhone; CPU iPhone OS 17_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Mobile/15E148 Safari/604.1",
                "Mozilla/5.0 (iPhone; CPU iPhone OS 16_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1",
                
                // Android Chrome
                "Mozilla/5.0 (Linux; Android 14; SM-G998B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36",
                "Mozilla/5.0 (Linux; Android 13; Pixel 7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36",
                
                // iPad
                "Mozilla/5.0 (iPad; CPU OS 17_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Mobile/15E148 Safari/604.1",
                "Mozilla/5.0 (iPad; CPU OS 16_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1",
            ],
            
            crawler_agents: vec![
                // Googlebot
                "Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)",
                "Mozilla/5.0 (Linux; Android 6.0.1; Nexus 5X Build/MMB29P) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)",
                
                // Bingbot
                "Mozilla/5.0 (compatible; bingbot/2.0; +http://www.bing.com/bingbot.htm)",
                "Mozilla/5.0 (iPhone; CPU iPhone OS 7_0 like Mac OS X) AppleWebKit/537.51.1 (KHTML, like Gecko) Version/7.0 Mobile/11A465 Safari/9537.53 (compatible; bingbot/2.0; +http://www.bing.com/bingbot.htm)",
                
                // Other crawlers
                "Mozilla/5.0 (compatible; YandexBot/3.0; +http://yandex.com/bots)",
                "Mozilla/5.0 (compatible; DuckDuckBot-Https/1.1; +https://duckduckgo.com/duckduckbot)",
                "facebookexternalhit/1.1 (+http://www.facebook.com/externalhit_uatext.php)",
                "Twitterbot/1.0",
            ],
            
            api_agents: vec![
                // Common API clients
                "curl/8.4.0",
                "wget/1.21.4",
                "HTTPie/3.2.2",
                "Postman/10.20.0",
                "insomnia/2023.8.0",
                "Python-urllib/3.11",
                "Python-requests/2.31.0",
                "Go-http-client/1.1",
                "Java/17.0.9",
                "okhttp/4.12.0",
                "axios/1.6.2",
                "node-fetch/3.3.2",
            ],
        }
    }

    pub fn get_random_browser_agent(&self) -> &'static str {
        let mut rng = rng();
        self.browser_agents
            .choose(&mut rng)
            .unwrap_or(&self.browser_agents[0])
    }

    pub fn get_random_mobile_agent(&self) -> &'static str {
        let mut rng = rng();
        self.mobile_agents
            .choose(&mut rng)
            .unwrap_or(&self.mobile_agents[0])
    }

    pub fn get_random_crawler_agent(&self) -> &'static str {
        let mut rng = rng();
        self.crawler_agents
            .choose(&mut rng)
            .unwrap_or(&self.crawler_agents[0])
    }

    pub fn get_random_api_agent(&self) -> &'static str {
        let mut rng = rng();
        self.api_agents
            .choose(&mut rng)
            .unwrap_or(&self.api_agents[0])
    }

    pub fn get_random_agent(&self) -> &'static str {
        let mut rng = rng();
        let all_agents: Vec<&'static str> = self
            .browser_agents
            .iter()
            .chain(self.mobile_agents.iter())
            .chain(self.crawler_agents.iter())
            .chain(self.api_agents.iter())
            .copied()
            .collect();

        all_agents
            .choose(&mut rng)
            .unwrap_or(&self.browser_agents[0])
    }

    pub fn get_agent_by_type(&self, agent_type: &str) -> &'static str {
        match agent_type.to_lowercase().as_str() {
            "browser" => self.get_random_browser_agent(),
            "mobile" => self.get_random_mobile_agent(),
            "crawler" => self.get_random_crawler_agent(),
            "api" => self.get_random_api_agent(),
            "random" => self.get_random_agent(),
            _ => self.get_random_browser_agent(),
        }
    }

    pub fn get_chrome_agent(&self) -> &'static str {
        let chrome_agents: Vec<&'static str> = self
            .browser_agents
            .iter()
            .filter(|agent| agent.contains("Chrome"))
            .copied()
            .collect();

        let mut rng = thread_rng();
        chrome_agents
            .choose(&mut rng)
            .unwrap_or(&self.browser_agents[0])
    }

    pub fn get_firefox_agent(&self) -> &'static str {
        let firefox_agents: Vec<&'static str> = self
            .browser_agents
            .iter()
            .filter(|agent| agent.contains("Firefox"))
            .copied()
            .collect();

        let mut rng = thread_rng();
        firefox_agents
            .choose(&mut rng)
            .unwrap_or(&self.browser_agents[3])
    }

    pub fn get_safari_agent(&self) -> &'static str {
        let safari_agents: Vec<&'static str> = self
            .browser_agents
            .iter()
            .filter(|agent| agent.contains("Safari") && !agent.contains("Chrome"))
            .copied()
            .collect();

        let mut rng = thread_rng();
        safari_agents
            .choose(&mut rng)
            .unwrap_or(&self.browser_agents[6])
    }

    pub fn get_stealth_agent(&self) -> &'static str {
        // 返回最常见的User-Agent，降低被检测的概率
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    }

    pub fn list_available_types(&self) -> Vec<&'static str> {
        vec![
            "browser", "mobile", "crawler", "api", "random", "chrome", "firefox", "safari",
            "stealth",
        ]
    }
}

impl Default for UserAgentGenerator {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_user_agent_generation() {
        let generator = UserAgentGenerator::new();

        // 测试各种类型的User-Agent生成
        assert!(!generator.get_random_browser_agent().is_empty());
        assert!(!generator.get_random_mobile_agent().is_empty());
        assert!(!generator.get_random_crawler_agent().is_empty());
        assert!(!generator.get_random_api_agent().is_empty());
        assert!(!generator.get_random_agent().is_empty());

        // 测试特定浏览器
        assert!(generator.get_chrome_agent().contains("Chrome"));
        assert!(generator.get_firefox_agent().contains("Firefox"));
        assert!(generator.get_safari_agent().contains("Safari"));

        // 测试类型选择
        assert!(!generator.get_agent_by_type("browser").is_empty());
        assert!(!generator.get_agent_by_type("mobile").is_empty());
        assert!(!generator.get_agent_by_type("unknown").is_empty()); // 应该返回默认值
    }

    #[test]
    fn test_stealth_agent() {
        let generator = UserAgentGenerator::new();
        let stealth_agent = generator.get_stealth_agent();

        // 隐蔽模式应该返回最常见的Chrome User-Agent
        assert!(stealth_agent.contains("Chrome"));
        assert!(stealth_agent.contains("Windows NT 10.0"));
    }
}
