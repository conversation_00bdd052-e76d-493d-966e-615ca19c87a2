use std::net::{IpAddr, Ipv4Addr, Ipv6Addr};
use std::str::FromStr;
use url::Url;

/// 验证IP地址是否有效
pub fn is_valid_ip(ip: &str) -> bool {
    IpAddr::from_str(ip).is_ok()
}

/// 验证IPv4地址是否有效
pub fn is_valid_ipv4(ip: &str) -> bool {
    Ipv4Addr::from_str(ip).is_ok()
}

/// 验证IPv6地址是否有效
pub fn is_valid_ipv6(ip: &str) -> bool {
    Ipv6Addr::from_str(ip).is_ok()
}

/// 验证域名是否有效
pub fn is_valid_domain(domain: &str) -> bool {
    if domain.is_empty() || domain.len() > 253 {
        return false;
    }

    // 基本的域名格式检查
    let parts: Vec<&str> = domain.split('.').collect();
    if parts.len() < 2 {
        return false;
    }

    for part in parts {
        if part.is_empty() || part.len() > 63 {
            return false;
        }

        // 检查是否只包含有效字符
        if !part.chars().all(|c| c.is_alphanumeric() || c == '-') {
            return false;
        }

        // 不能以连字符开头或结尾
        if part.starts_with('-') || part.ends_with('-') {
            return false;
        }
    }

    true
}

/// 验证URL是否有效
pub fn is_valid_url(url: &str) -> bool {
    Url::parse(url).is_ok()
}

/// 验证端口范围是否有效
pub fn is_valid_port_range(port_range: &str) -> bool {
    if port_range.is_empty() {
        return false;
    }

    // 支持的格式: "80", "80,443", "1-1000", "80,443,8080-8090"
    for part in port_range.split(',') {
        let part = part.trim();

        if part.contains('-') {
            // 端口范围
            let range_parts: Vec<&str> = part.split('-').collect();
            if range_parts.len() != 2 {
                return false;
            }

            let start: u16 = match range_parts[0].parse() {
                Ok(port) => port,
                Err(_) => return false,
            };

            let end: u16 = match range_parts[1].parse() {
                Ok(port) => port,
                Err(_) => return false,
            };

            if start > end || start == 0 || end == 0 {
                return false;
            }
        } else {
            // 单个端口
            let port: u16 = match part.parse() {
                Ok(port) => port,
                Err(_) => return false,
            };

            if port == 0 {
                return false;
            }
        }
    }

    true
}

/// 解析端口范围为端口列表
pub fn parse_port_range(port_range: &str) -> Result<Vec<u16>, String> {
    let mut ports = Vec::new();

    for part in port_range.split(',') {
        let part = part.trim();

        if part.contains('-') {
            // 端口范围
            let range_parts: Vec<&str> = part.split('-').collect();
            if range_parts.len() != 2 {
                return Err(format!("Invalid port range format: {}", part));
            }

            let start: u16 = range_parts[0]
                .parse()
                .map_err(|_| format!("Invalid start port: {}", range_parts[0]))?;
            let end: u16 = range_parts[1]
                .parse()
                .map_err(|_| format!("Invalid end port: {}", range_parts[1]))?;

            if start > end {
                return Err(format!(
                    "Start port {} is greater than end port {}",
                    start, end
                ));
            }

            for port in start..=end {
                ports.push(port);
            }
        } else {
            // 单个端口
            let port: u16 = part
                .parse()
                .map_err(|_| format!("Invalid port: {}", part))?;
            ports.push(port);
        }
    }

    // 去重并排序
    ports.sort_unstable();
    ports.dedup();

    Ok(ports)
}

/// 格式化文件大小
pub fn format_file_size(size: u64) -> String {
    const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB"];
    let mut size = size as f64;
    let mut unit_index = 0;

    while size >= 1024.0 && unit_index < UNITS.len() - 1 {
        size /= 1024.0;
        unit_index += 1;
    }

    if unit_index == 0 {
        format!("{} {}", size as u64, UNITS[unit_index])
    } else {
        format!("{:.2} {}", size, UNITS[unit_index])
    }
}

/// 格式化持续时间
pub fn format_duration(seconds: u64) -> String {
    let hours = seconds / 3600;
    let minutes = (seconds % 3600) / 60;
    let secs = seconds % 60;

    if hours > 0 {
        format!("{}h {}m {}s", hours, minutes, secs)
    } else if minutes > 0 {
        format!("{}m {}s", minutes, secs)
    } else {
        format!("{}s", secs)
    }
}

/// 清理和验证目标输入
pub fn sanitize_target(target: &str) -> Result<String, String> {
    let target = target.trim();

    if target.is_empty() {
        return Err("Target cannot be empty".to_string());
    }

    // 移除协议前缀（如果存在）
    let target = if target.starts_with("http://") {
        &target[7..]
    } else if target.starts_with("https://") {
        &target[8..]
    } else {
        target
    };

    // 移除路径部分（如果存在）
    let target = if let Some(slash_pos) = target.find('/') {
        &target[..slash_pos]
    } else {
        target
    };

    // 移除端口部分（如果存在）
    let target = if let Some(colon_pos) = target.rfind(':') {
        // 检查是否是IPv6地址
        if target.starts_with('[') && target.contains(']') {
            target // IPv6地址，保持原样
        } else if target.chars().filter(|&c| c == ':').count() == 1 {
            // 只有一个冒号，可能是端口
            &target[..colon_pos]
        } else {
            target // 可能是IPv6地址，保持原样
        }
    } else {
        target
    };

    let target = target.to_string();

    // 验证目标格式
    if is_valid_ip(&target) || is_valid_domain(&target) {
        Ok(target)
    } else {
        Err(format!("Invalid target format: {}", target))
    }
}

/// 生成随机字符串
pub fn generate_random_string(length: usize) -> String {
    use rand::Rng;
    const CHARSET: &[u8] = b"abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    let mut rng = rand::thread_rng();

    (0..length)
        .map(|_| {
            let idx = rng.gen_range(0..CHARSET.len());
            CHARSET[idx] as char
        })
        .collect()
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_ip_validation() {
        assert!(is_valid_ip("***********"));
        assert!(is_valid_ip("::1"));
        assert!(!is_valid_ip("256.256.256.256"));
        assert!(!is_valid_ip("invalid"));
    }

    #[test]
    fn test_domain_validation() {
        assert!(is_valid_domain("example.com"));
        assert!(is_valid_domain("sub.example.com"));
        assert!(!is_valid_domain(""));
        assert!(!is_valid_domain("invalid"));
        assert!(!is_valid_domain(".com"));
    }

    #[test]
    fn test_port_range_validation() {
        assert!(is_valid_port_range("80"));
        assert!(is_valid_port_range("80,443"));
        assert!(is_valid_port_range("1-1000"));
        assert!(is_valid_port_range("80,443,8080-8090"));
        assert!(!is_valid_port_range(""));
        assert!(!is_valid_port_range("0"));
        assert!(!is_valid_port_range("1000-100"));
    }

    #[test]
    fn test_port_range_parsing() {
        let ports = parse_port_range("80,443,8080-8082").unwrap();
        assert_eq!(ports, vec![80, 443, 8080, 8081, 8082]);

        let ports = parse_port_range("22").unwrap();
        assert_eq!(ports, vec![22]);

        assert!(parse_port_range("invalid").is_err());
    }

    #[test]
    fn test_target_sanitization() {
        assert_eq!(sanitize_target("example.com").unwrap(), "example.com");
        assert_eq!(
            sanitize_target("http://example.com").unwrap(),
            "example.com"
        );
        assert_eq!(
            sanitize_target("https://example.com/path").unwrap(),
            "example.com"
        );
        assert_eq!(sanitize_target("example.com:8080").unwrap(), "example.com");
        assert!(sanitize_target("").is_err());
    }

    #[test]
    fn test_format_file_size() {
        assert_eq!(format_file_size(1024), "1.00 KB");
        assert_eq!(format_file_size(1048576), "1.00 MB");
        assert_eq!(format_file_size(500), "500 B");
    }

    #[test]
    fn test_format_duration() {
        assert_eq!(format_duration(3661), "1h 1m 1s");
        assert_eq!(format_duration(61), "1m 1s");
        assert_eq!(format_duration(30), "30s");
    }
}
