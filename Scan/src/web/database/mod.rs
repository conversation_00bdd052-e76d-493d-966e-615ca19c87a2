use sqlx::{sqlite::SqlitePool, Pool, Sqlite};
use std::time::Duration;

pub mod migrations;

#[derive(Debu<PERSON>, Clone)]
pub struct Database {
    pool: Pool<Sqlite>,
}

impl Database {
    pub async fn new(database_url: &str) -> Result<Self, sqlx::Error> {
        let pool = SqlitePool::connect_with(
            sqlx::sqlite::SqliteConnectOptions::new()
                .filename(database_url.strip_prefix("sqlite:").unwrap_or(database_url))
                .create_if_missing(true)
                .busy_timeout(Duration::from_secs(30)),
        )
        .await?;

        Ok(Self { pool })
    }

    pub async fn migrate(&self) -> Result<(), sqlx::Error> {
        migrations::run_migrations(&self.pool).await
    }

    pub fn pool(&self) -> &Pool<Sqlite> {
        &self.pool
    }
}
