use chrono::Utc;
use log::{error, info};
use sqlx::{Pool, Sqlite};

use crate::web::database::Database;
use crate::web::models::{CreateTaskRequest, Task, TaskListResponse, TaskQuery, TaskStatus};

pub struct TaskService<'a> {
    db: &'a Database,
}

impl<'a> TaskService<'a> {
    pub fn new(db: &'a Database) -> Self {
        Self { db }
    }

    pub async fn create_task(&self, request: CreateTaskRequest) -> Result<Task, sqlx::Error> {
        let task = Task::new(request);

        sqlx::query!(
            r#"
            INSERT INTO tasks (id, name, target, scan_type, status, config, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?)
            "#,
            task.id,
            task.name,
            task.target,
            task.scan_type,
            task.status.to_string(),
            task.config,
            task.created_at
        )
        .execute(self.db.pool())
        .await?;

        Ok(task)
    }

    pub async fn list_tasks(&self, query: TaskQuery) -> Result<TaskListResponse, sqlx::Error> {
        let page = query.page.unwrap_or(1);
        let per_page = query.per_page.unwrap_or(20);
        let offset = (page - 1) * per_page;

        let mut where_conditions = Vec::new();
        let mut params: Vec<Box<dyn sqlx::Encode<'_, Sqlite> + Send + Sync>> = Vec::new();

        if let Some(status) = &query.status {
            where_conditions.push("status = ?");
            params.push(Box::new(status.to_string()));
        }

        if let Some(scan_type) = &query.scan_type {
            where_conditions.push("scan_type = ?");
            params.push(Box::new(scan_type.clone()));
        }

        let where_clause = if where_conditions.is_empty() {
            String::new()
        } else {
            format!("WHERE {}", where_conditions.join(" AND "))
        };

        // 获取总数
        let count_query = format!("SELECT COUNT(*) as count FROM tasks {}", where_clause);
        let total: i64 = sqlx::query_scalar(&count_query)
            .execute(self.db.pool())
            .await?
            .unwrap_or(0);

        // 获取任务列表
        let list_query = format!(
            "SELECT * FROM tasks {} ORDER BY created_at DESC LIMIT ? OFFSET ?",
            where_clause
        );

        let mut query_builder = sqlx::query_as::<_, Task>(&list_query);

        // 添加参数
        for param in params {
            // 这里需要根据实际参数类型来处理
            // 由于类型擦除的限制，我们需要重新构建查询
        }

        // 简化版本：不使用动态参数
        let tasks = if let Some(status) = &query.status {
            sqlx::query_as!(
                Task,
                "SELECT * FROM tasks WHERE status = ? ORDER BY created_at DESC LIMIT ? OFFSET ?",
                status.to_string(),
                per_page,
                offset
            )
            .fetch_all(self.db.pool())
            .await?
        } else {
            sqlx::query_as!(
                Task,
                "SELECT * FROM tasks ORDER BY created_at DESC LIMIT ? OFFSET ?",
                per_page,
                offset
            )
            .fetch_all(self.db.pool())
            .await?
        };

        Ok(TaskListResponse {
            tasks,
            total,
            page,
            per_page,
        })
    }

    pub async fn get_task(&self, task_id: &str) -> Result<Option<Task>, sqlx::Error> {
        let task = sqlx::query_as!(Task, "SELECT * FROM tasks WHERE id = ?", task_id)
            .fetch_optional(self.db.pool())
            .await?;

        Ok(task)
    }

    pub async fn start_task(&self, task_id: &str) -> Result<Task, Box<dyn std::error::Error>> {
        // 更新任务状态为运行中
        let now = Utc::now();
        sqlx::query!(
            "UPDATE tasks SET status = ?, started_at = ? WHERE id = ?",
            TaskStatus::Running.to_string(),
            now,
            task_id
        )
        .execute(self.db.pool())
        .await?;

        // 获取更新后的任务
        let task = self.get_task(task_id).await?.ok_or("Task not found")?;

        // 启动扫描任务（异步）
        let task_clone = task.clone();
        tokio::spawn(async move {
            if let Err(e) = run_scan_task(task_clone).await {
                error!("Scan task failed: {}", e);
            }
        });

        Ok(task)
    }

    pub async fn stop_task(&self, task_id: &str) -> Result<Task, Box<dyn std::error::Error>> {
        // 更新任务状态为已取消
        let now = Utc::now();
        sqlx::query!(
            "UPDATE tasks SET status = ?, completed_at = ? WHERE id = ?",
            TaskStatus::Cancelled.to_string(),
            now,
            task_id
        )
        .execute(self.db.pool())
        .await?;

        // 获取更新后的任务
        let task = self.get_task(task_id).await?.ok_or("Task not found")?;

        Ok(task)
    }

    pub async fn delete_task(&self, task_id: &str) -> Result<(), sqlx::Error> {
        sqlx::query!("DELETE FROM tasks WHERE id = ?", task_id)
            .execute(self.db.pool())
            .await?;

        Ok(())
    }

    pub async fn update_task_status(
        &self,
        task_id: &str,
        status: TaskStatus,
        error_message: Option<String>,
    ) -> Result<(), sqlx::Error> {
        let now = Utc::now();

        match status {
            TaskStatus::Completed | TaskStatus::Failed | TaskStatus::Cancelled => {
                sqlx::query!(
                    "UPDATE tasks SET status = ?, completed_at = ?, error_message = ? WHERE id = ?",
                    status.to_string(),
                    now,
                    error_message,
                    task_id
                )
                .execute(self.db.pool())
                .await?;
            }
            _ => {
                sqlx::query!(
                    "UPDATE tasks SET status = ?, error_message = ? WHERE id = ?",
                    status.to_string(),
                    error_message,
                    task_id
                )
                .execute(self.db.pool())
                .await?;
            }
        }

        Ok(())
    }
}

async fn run_scan_task(task: Task) -> Result<(), Box<dyn std::error::Error>> {
    info!("Starting scan task: {} ({})", task.name, task.id);

    // TODO: 实现实际的扫描逻辑
    // 这里应该调用扫描工具集成模块

    // 模拟扫描过程
    tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;

    info!("Scan task completed: {} ({})", task.name, task.id);

    Ok(())
}
