use log::{error, info};
use std::sync::Arc;
use tokio::sync::Mutex;

use crate::web::database::Database;
use crate::web::models::{ScanConfig, Task, TaskStatus};
use crate::web::services::TaskService;
use crate::web::tools::WorkflowCoordinator;

pub struct ScanService {
    db: Arc<Database>,
    workflow_coordinator: Arc<Mutex<WorkflowCoordinator>>,
}

impl ScanService {
    pub fn new(db: Arc<Database>) -> Self {
        let workflow_coordinator = Arc::new(Mutex::new(WorkflowCoordinator::new()));

        Self {
            db,
            workflow_coordinator,
        }
    }

    pub async fn execute_scan(&self, task: Task) -> Result<(), Box<dyn std::error::Error>> {
        info!(
            "Starting scan execution for task: {} ({})",
            task.name, task.id
        );

        let task_service = TaskService::new(&self.db);

        // 更新任务状态为运行中
        if let Err(e) = task_service
            .update_task_status(&task.id, TaskStatus::Running, None)
            .await
        {
            error!("Failed to update task status to running: {}", e);
            return Err(e.into());
        }

        // 解析扫描配置
        let config = match task.get_config() {
            Ok(config) => config,
            Err(e) => {
                error!("Failed to parse scan config: {}", e);
                task_service
                    .update_task_status(
                        &task.id,
                        TaskStatus::Failed,
                        Some(format!("Invalid scan configuration: {}", e)),
                    )
                    .await?;
                return Err(e.into());
            }
        };

        // 执行扫描工作流
        let result = self.run_scan_workflow(&task, &config).await;

        // 更新任务状态
        match result {
            Ok(_) => {
                info!("Scan completed successfully for task: {}", task.id);
                task_service
                    .update_task_status(&task.id, TaskStatus::Completed, None)
                    .await?;
            }
            Err(e) => {
                error!("Scan failed for task {}: {}", task.id, e);
                task_service
                    .update_task_status(&task.id, TaskStatus::Failed, Some(e.to_string()))
                    .await?;
                return Err(e);
            }
        }

        Ok(())
    }

    async fn run_scan_workflow(
        &self,
        task: &Task,
        config: &ScanConfig,
    ) -> Result<(), Box<dyn std::error::Error>> {
        let mut coordinator = self.workflow_coordinator.lock().await;

        // 根据扫描类型选择工作流
        match task.scan_type.as_str() {
            "quick" => {
                coordinator
                    .run_quick_scan(&task.id, &task.target, config)
                    .await
            }
            "standard" => {
                coordinator
                    .run_standard_scan(&task.id, &task.target, config)
                    .await
            }
            "deep" => {
                coordinator
                    .run_deep_scan(&task.id, &task.target, config)
                    .await
            }
            "web_focused" => {
                coordinator
                    .run_web_focused_scan(&task.id, &task.target, config)
                    .await
            }
            "custom" => {
                coordinator
                    .run_custom_scan(&task.id, &task.target, config)
                    .await
            }
            _ => {
                error!("Unknown scan type: {}", task.scan_type);
                Err("Unknown scan type".into())
            }
        }
    }

    pub async fn cancel_scan(&self, task_id: &str) -> Result<(), Box<dyn std::error::Error>> {
        info!("Cancelling scan for task: {}", task_id);

        let mut coordinator = self.workflow_coordinator.lock().await;
        coordinator.cancel_scan(task_id).await?;

        let task_service = TaskService::new(&self.db);
        task_service
            .update_task_status(task_id, TaskStatus::Cancelled, None)
            .await?;

        Ok(())
    }

    pub async fn get_scan_progress(
        &self,
        task_id: &str,
    ) -> Result<f32, Box<dyn std::error::Error>> {
        let coordinator = self.workflow_coordinator.lock().await;
        Ok(coordinator.get_progress(task_id).await.unwrap_or(0.0))
    }
}
