use log::info;
use sqlx::{Pool, Sqlite};

use crate::web::database::Database;
use crate::web::models::{
    PaginationQuery, PortResult, ResultSummary, SubdomainResult, TaskResults, VulnerabilityResult,
    WebResult,
};

pub struct ResultService<'a> {
    db: &'a Database,
}

impl<'a> ResultService<'a> {
    pub fn new(db: &'a Database) -> Self {
        Self { db }
    }

    pub async fn get_task_results(&self, task_id: &str) -> Result<TaskResults, sqlx::Error> {
        // 获取端口结果
        let ports = sqlx::query_as!(
            PortResult,
            "SELECT * FROM port_results WHERE task_id = ? ORDER BY created_at DESC",
            task_id
        )
        .fetch_all(self.db.pool())
        .await?;

        // 获取子域名结果
        let subdomains = sqlx::query_as!(
            SubdomainResult,
            "SELECT * FROM subdomain_results WHERE task_id = ? ORDER BY created_at DESC",
            task_id
        )
        .fetch_all(self.db.pool())
        .await?;

        // 获取Web资产结果
        let web_assets = sqlx::query_as!(
            WebResult,
            "SELECT * FROM web_results WHERE task_id = ? ORDER BY created_at DESC",
            task_id
        )
        .fetch_all(self.db.pool())
        .await?;

        // 获取漏洞结果
        let vulnerabilities = sqlx::query_as!(
            VulnerabilityResult,
            "SELECT * FROM vulnerability_results WHERE task_id = ? ORDER BY created_at DESC",
            task_id
        )
        .fetch_all(self.db.pool())
        .await?;

        // 生成摘要
        let summary = self.generate_summary(&ports, &subdomains, &web_assets, &vulnerabilities);

        Ok(TaskResults {
            task_id: task_id.to_string(),
            ports,
            subdomains,
            web_assets,
            vulnerabilities,
            summary,
        })
    }

    pub async fn get_port_results(
        &self,
        task_id: &str,
        pagination: PaginationQuery,
    ) -> Result<Vec<PortResult>, sqlx::Error> {
        let limit = pagination.get_limit();
        let offset = pagination.get_offset();

        let results = sqlx::query_as!(
            PortResult,
            "SELECT * FROM port_results WHERE task_id = ? ORDER BY created_at DESC LIMIT ? OFFSET ?",
            task_id,
            limit,
            offset
        )
        .fetch_all(self.db.pool())
        .await?;

        Ok(results)
    }

    pub async fn get_subdomain_results(
        &self,
        task_id: &str,
        pagination: PaginationQuery,
    ) -> Result<Vec<SubdomainResult>, sqlx::Error> {
        let limit = pagination.get_limit();
        let offset = pagination.get_offset();

        let results = sqlx::query_as!(
            SubdomainResult,
            "SELECT * FROM subdomain_results WHERE task_id = ? ORDER BY created_at DESC LIMIT ? OFFSET ?",
            task_id,
            limit,
            offset
        )
        .fetch_all(self.db.pool())
        .await?;

        Ok(results)
    }

    pub async fn get_web_results(
        &self,
        task_id: &str,
        pagination: PaginationQuery,
    ) -> Result<Vec<WebResult>, sqlx::Error> {
        let limit = pagination.get_limit();
        let offset = pagination.get_offset();

        let results = sqlx::query_as!(
            WebResult,
            "SELECT * FROM web_results WHERE task_id = ? ORDER BY created_at DESC LIMIT ? OFFSET ?",
            task_id,
            limit,
            offset
        )
        .fetch_all(self.db.pool())
        .await?;

        Ok(results)
    }

    pub async fn get_vulnerability_results(
        &self,
        task_id: &str,
        pagination: PaginationQuery,
    ) -> Result<Vec<VulnerabilityResult>, sqlx::Error> {
        let limit = pagination.get_limit();
        let offset = pagination.get_offset();

        let results = sqlx::query_as!(
            VulnerabilityResult,
            "SELECT * FROM vulnerability_results WHERE task_id = ? ORDER BY created_at DESC LIMIT ? OFFSET ?",
            task_id,
            limit,
            offset
        )
        .fetch_all(self.db.pool())
        .await?;

        Ok(results)
    }

    pub async fn export_results(
        &self,
        task_id: &str,
        format: &str,
    ) -> Result<(String, String, String), Box<dyn std::error::Error>> {
        let results = self.get_task_results(task_id).await?;

        match format.to_lowercase().as_str() {
            "json" => {
                let content = serde_json::to_string_pretty(&results)?;
                Ok((
                    content,
                    "application/json".to_string(),
                    format!("scan_results_{}.json", task_id),
                ))
            }
            "csv" => {
                let content = self.export_to_csv(&results)?;
                Ok((
                    content,
                    "text/csv".to_string(),
                    format!("scan_results_{}.csv", task_id),
                ))
            }
            "markdown" => {
                let content = self.export_to_markdown(&results)?;
                Ok((
                    content,
                    "text/markdown".to_string(),
                    format!("scan_results_{}.md", task_id),
                ))
            }
            _ => Err("Unsupported export format".into()),
        }
    }

    fn generate_summary(
        &self,
        ports: &[PortResult],
        subdomains: &[SubdomainResult],
        web_assets: &[WebResult],
        vulnerabilities: &[VulnerabilityResult],
    ) -> ResultSummary {
        let open_ports = ports.iter().filter(|p| p.status == "open").count();
        let high_severity_vulns = vulnerabilities
            .iter()
            .filter(|v| v.severity == "high")
            .count();
        let medium_severity_vulns = vulnerabilities
            .iter()
            .filter(|v| v.severity == "medium")
            .count();
        let low_severity_vulns = vulnerabilities
            .iter()
            .filter(|v| v.severity == "low")
            .count();

        ResultSummary {
            total_ports: ports.len(),
            open_ports,
            total_subdomains: subdomains.len(),
            total_web_assets: web_assets.len(),
            total_vulnerabilities: vulnerabilities.len(),
            high_severity_vulns,
            medium_severity_vulns,
            low_severity_vulns,
        }
    }

    fn export_to_csv(&self, results: &TaskResults) -> Result<String, Box<dyn std::error::Error>> {
        let mut csv_content = String::new();

        // 端口结果
        csv_content.push_str("Type,IP Address,Port,Protocol,Status,Service,Version\n");
        for port in &results.ports {
            csv_content.push_str(&format!(
                "Port,{},{},{},{},{},{}\n",
                port.ip_address,
                port.port,
                port.protocol,
                port.status,
                port.service.as_deref().unwrap_or(""),
                port.version.as_deref().unwrap_or("")
            ));
        }

        // 子域名结果
        csv_content.push_str("\nType,Domain,IP Address,Source\n");
        for subdomain in &results.subdomains {
            csv_content.push_str(&format!(
                "Subdomain,{},{},{}\n",
                subdomain.domain,
                subdomain.ip_address.as_deref().unwrap_or(""),
                subdomain.source.as_deref().unwrap_or("")
            ));
        }

        Ok(csv_content)
    }

    fn export_to_markdown(
        &self,
        results: &TaskResults,
    ) -> Result<String, Box<dyn std::error::Error>> {
        let mut md_content = String::new();

        md_content.push_str(&format!("# Scan Results for Task {}\n\n", results.task_id));

        // 摘要
        md_content.push_str("## Summary\n\n");
        md_content.push_str(&format!(
            "- **Total Ports**: {}\n",
            results.summary.total_ports
        ));
        md_content.push_str(&format!(
            "- **Open Ports**: {}\n",
            results.summary.open_ports
        ));
        md_content.push_str(&format!(
            "- **Subdomains**: {}\n",
            results.summary.total_subdomains
        ));
        md_content.push_str(&format!(
            "- **Web Assets**: {}\n",
            results.summary.total_web_assets
        ));
        md_content.push_str(&format!(
            "- **Vulnerabilities**: {}\n\n",
            results.summary.total_vulnerabilities
        ));

        // 端口结果
        if !results.ports.is_empty() {
            md_content.push_str("## Port Scan Results\n\n");
            md_content.push_str("| IP Address | Port | Protocol | Status | Service | Version |\n");
            md_content.push_str("|------------|------|----------|--------|---------|----------|\n");

            for port in &results.ports {
                md_content.push_str(&format!(
                    "| {} | {} | {} | {} | {} | {} |\n",
                    port.ip_address,
                    port.port,
                    port.protocol,
                    port.status,
                    port.service.as_deref().unwrap_or("-"),
                    port.version.as_deref().unwrap_or("-")
                ));
            }
            md_content.push_str("\n");
        }

        Ok(md_content)
    }
}
