use actix_web::{web, HttpResponse, Result};
use log::error;

use crate::web::database::Database;
use crate::web::models::{ApiResponse, PaginationQuery};
use crate::web::services::ResultService;

pub fn configure_routes(cfg: &mut web::ServiceConfig) {
    cfg.route("/tasks/{id}/results", web::get().to(get_task_results))
        .route("/tasks/{id}/results/ports", web::get().to(get_port_results))
        .route(
            "/tasks/{id}/results/subdomains",
            web::get().to(get_subdomain_results),
        )
        .route("/tasks/{id}/results/web", web::get().to(get_web_results))
        .route(
            "/tasks/{id}/results/vulnerabilities",
            web::get().to(get_vulnerability_results),
        )
        .route(
            "/tasks/{id}/results/export/{format}",
            web::get().to(export_results),
        );
}

async fn get_task_results(
    db: web::Data<Database>,
    path: web::Path<String>,
) -> Result<HttpResponse> {
    let task_id = path.into_inner();
    let result_service = ResultService::new(db.get_ref());

    match result_service.get_task_results(&task_id).await {
        Ok(results) => Ok(HttpResponse::Ok().json(ApiResponse::success(results))),
        Err(e) => {
            error!("Failed to get results for task {}: {}", task_id, e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(format!(
                    "Failed to get results: {}",
                    e
                ))),
            )
        }
    }
}

async fn get_port_results(
    db: web::Data<Database>,
    path: web::Path<String>,
    query: web::Query<PaginationQuery>,
) -> Result<HttpResponse> {
    let task_id = path.into_inner();
    let result_service = ResultService::new(db.get_ref());

    match result_service
        .get_port_results(&task_id, query.into_inner())
        .await
    {
        Ok(results) => Ok(HttpResponse::Ok().json(ApiResponse::success(results))),
        Err(e) => {
            error!("Failed to get port results for task {}: {}", task_id, e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(format!(
                    "Failed to get port results: {}",
                    e
                ))),
            )
        }
    }
}

async fn get_subdomain_results(
    db: web::Data<Database>,
    path: web::Path<String>,
    query: web::Query<PaginationQuery>,
) -> Result<HttpResponse> {
    let task_id = path.into_inner();
    let result_service = ResultService::new(db.get_ref());

    match result_service
        .get_subdomain_results(&task_id, query.into_inner())
        .await
    {
        Ok(results) => Ok(HttpResponse::Ok().json(ApiResponse::success(results))),
        Err(e) => {
            error!(
                "Failed to get subdomain results for task {}: {}",
                task_id, e
            );
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(format!(
                    "Failed to get subdomain results: {}",
                    e
                ))),
            )
        }
    }
}

async fn get_web_results(
    db: web::Data<Database>,
    path: web::Path<String>,
    query: web::Query<PaginationQuery>,
) -> Result<HttpResponse> {
    let task_id = path.into_inner();
    let result_service = ResultService::new(db.get_ref());

    match result_service
        .get_web_results(&task_id, query.into_inner())
        .await
    {
        Ok(results) => Ok(HttpResponse::Ok().json(ApiResponse::success(results))),
        Err(e) => {
            error!("Failed to get web results for task {}: {}", task_id, e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(format!(
                    "Failed to get web results: {}",
                    e
                ))),
            )
        }
    }
}

async fn get_vulnerability_results(
    db: web::Data<Database>,
    path: web::Path<String>,
    query: web::Query<PaginationQuery>,
) -> Result<HttpResponse> {
    let task_id = path.into_inner();
    let result_service = ResultService::new(db.get_ref());

    match result_service
        .get_vulnerability_results(&task_id, query.into_inner())
        .await
    {
        Ok(results) => Ok(HttpResponse::Ok().json(ApiResponse::success(results))),
        Err(e) => {
            error!(
                "Failed to get vulnerability results for task {}: {}",
                task_id, e
            );
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(format!(
                    "Failed to get vulnerability results: {}",
                    e
                ))),
            )
        }
    }
}

async fn export_results(
    db: web::Data<Database>,
    path: web::Path<(String, String)>,
) -> Result<HttpResponse> {
    let (task_id, format) = path.into_inner();
    let result_service = ResultService::new(db.get_ref());

    match result_service.export_results(&task_id, &format).await {
        Ok((content, content_type, filename)) => Ok(HttpResponse::Ok()
            .content_type(content_type)
            .insert_header((
                "Content-Disposition",
                format!("attachment; filename=\"{}\"", filename),
            ))
            .body(content)),
        Err(e) => {
            error!(
                "Failed to export results for task {} in format {}: {}",
                task_id, format, e
            );
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(format!(
                    "Failed to export results: {}",
                    e
                ))),
            )
        }
    }
}
