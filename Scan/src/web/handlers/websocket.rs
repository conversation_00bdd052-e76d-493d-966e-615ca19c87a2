use actix_web::{web, HttpRequest, HttpResponse, Result};
use actix_ws::{Message, MessageStream, Session};
use futures_util::StreamExt;
use log::{error, info, warn};
use serde::{Deserialize, Serialize};
use std::time::{Duration, Instant};

pub fn configure_routes(cfg: &mut web::ServiceConfig) {
    cfg.route("/ws", web::get().to(websocket_handler));
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WebSocketMessage {
    pub message_type: String,
    pub task_id: Option<String>,
    pub data: serde_json::Value,
    pub timestamp: String,
}

const HEARTBEAT_INTERVAL: Duration = Duration::from_secs(5);
const CLIENT_TIMEOUT: Duration = Duration::from_secs(10);

pub async fn websocket_handler(req: HttpRequest, stream: web::Payload) -> Result<HttpResponse> {
    let (response, session, mut msg_stream) = actix_ws::handle(&req, stream)?;

    info!("WebSocket connection established");

    // 启动心跳和消息处理
    actix_web::rt::spawn(websocket_heartbeat(session.clone()));
    actix_web::rt::spawn(handle_websocket_messages(session, msg_stream));

    Ok(response)
}

async fn websocket_heartbeat(mut session: Session) {
    let mut last_heartbeat = Instant::now();
    let mut interval = actix_web::rt::time::interval(HEARTBEAT_INTERVAL);

    loop {
        interval.tick().await;

        // 检查客户端是否超时
        if Instant::now().duration_since(last_heartbeat) > CLIENT_TIMEOUT {
            warn!("WebSocket client timeout, closing connection");
            let _ = session.close(None).await;
            return;
        }

        // 发送ping消息
        if session.ping(b"").await.is_err() {
            error!("Failed to send ping, closing connection");
            return;
        }
    }
}

async fn handle_websocket_messages(mut session: Session, mut msg_stream: MessageStream) {
    while let Some(Ok(msg)) = msg_stream.next().await {
        match msg {
            Message::Ping(bytes) => {
                if session.pong(&bytes).await.is_err() {
                    error!("Failed to send pong response");
                    return;
                }
            }
            Message::Pong(_) => {
                // 客户端响应了ping，更新心跳时间
            }
            Message::Text(text) => {
                if let Err(e) = handle_text_message(&mut session, text.to_string()).await {
                    error!("Error handling text message: {}", e);
                }
            }
            Message::Binary(_) => {
                warn!("Received binary message, ignoring");
            }
            Message::Close(reason) => {
                info!("WebSocket connection closed: {:?}", reason);
                return;
            }
            _ => {}
        }
    }
}

async fn handle_text_message(
    session: &mut Session,
    text: String,
) -> Result<(), Box<dyn std::error::Error>> {
    // 尝试解析消息
    let message: WebSocketMessage = match serde_json::from_str(&text) {
        Ok(msg) => msg,
        Err(e) => {
            error!("Failed to parse WebSocket message: {}", e);
            send_error_message(session, "Invalid message format").await?;
            return Ok(());
        }
    };

    match message.message_type.as_str() {
        "subscribe_task" => {
            if let Some(task_id) = message.task_id {
                info!("Client subscribed to task: {}", task_id);
                send_success_message(session, "subscribed", &task_id).await?;
            } else {
                send_error_message(session, "Task ID required for subscription").await?;
            }
        }
        "unsubscribe_task" => {
            if let Some(task_id) = message.task_id {
                info!("Client unsubscribed from task: {}", task_id);
                send_success_message(session, "unsubscribed", &task_id).await?;
            } else {
                send_error_message(session, "Task ID required for unsubscription").await?;
            }
        }
        "ping" => {
            send_pong_message(session).await?;
        }
        _ => {
            warn!("Unknown message type: {}", message.message_type);
            send_error_message(session, "Unknown message type").await?;
        }
    }

    Ok(())
}

async fn send_success_message(
    session: &mut Session,
    action: &str,
    task_id: &str,
) -> Result<(), Box<dyn std::error::Error>> {
    let message = WebSocketMessage {
        message_type: "success".to_string(),
        task_id: Some(task_id.to_string()),
        data: serde_json::json!({
            "action": action,
            "message": format!("Successfully {} task {}", action, task_id)
        }),
        timestamp: chrono::Utc::now().to_rfc3339(),
    };

    let text = serde_json::to_string(&message)?;
    session.text(text).await?;
    Ok(())
}

async fn send_error_message(
    session: &mut Session,
    error: &str,
) -> Result<(), Box<dyn std::error::Error>> {
    let message = WebSocketMessage {
        message_type: "error".to_string(),
        task_id: None,
        data: serde_json::json!({
            "error": error
        }),
        timestamp: chrono::Utc::now().to_rfc3339(),
    };

    let text = serde_json::to_string(&message)?;
    session.text(text).await?;
    Ok(())
}

async fn send_pong_message(session: &mut Session) -> Result<(), Box<dyn std::error::Error>> {
    let message = WebSocketMessage {
        message_type: "pong".to_string(),
        task_id: None,
        data: serde_json::json!({}),
        timestamp: chrono::Utc::now().to_rfc3339(),
    };

    let text = serde_json::to_string(&message)?;
    session.text(text).await?;
    Ok(())
}

// 用于向特定任务的订阅者广播消息的函数
pub async fn broadcast_task_update(
    task_id: &str,
    update_type: &str,
    data: serde_json::Value,
) -> Result<(), Box<dyn std::error::Error>> {
    let message = WebSocketMessage {
        message_type: "task_update".to_string(),
        task_id: Some(task_id.to_string()),
        data: serde_json::json!({
            "update_type": update_type,
            "data": data
        }),
        timestamp: chrono::Utc::now().to_rfc3339(),
    };

    // TODO: 实现实际的广播逻辑
    // 这里需要维护一个连接池来跟踪订阅了特定任务的客户端
    info!(
        "Broadcasting task update: {}",
        serde_json::to_string(&message)?
    );

    Ok(())
}
