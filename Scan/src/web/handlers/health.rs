use actix_web::{web, HttpResponse, Result};
use serde_json::json;

pub fn configure_routes(cfg: &mut web::ServiceConfig) {
    cfg.route("/health", web::get().to(health_check))
        .route("/version", web::get().to(version_info));
}

async fn health_check() -> Result<HttpResponse> {
    Ok(HttpResponse::Ok().json(json!({
        "status": "healthy",
        "timestamp": chrono::Utc::now().to_rfc3339(),
        "service": "rustscan-web"
    })))
}

async fn version_info() -> Result<HttpResponse> {
    Ok(HttpResponse::Ok().json(json!({
        "name": env!("CARGO_PKG_NAME"),
        "version": env!("CARGO_PKG_VERSION"),
        "description": env!("CARGO_PKG_DESCRIPTION"),
        "authors": env!("CARGO_PKG_AUTHORS").split(':').collect::<Vec<&str>>(),
        "repository": env!("CARGO_PKG_REPOSITORY")
    })))
}
