use actix_web::{web, HttpResponse, Result};
use log::{error, info};

use crate::web::database::Database;
use crate::web::models::{ApiResponse, CreateTaskRequest, Task, TaskListResponse, TaskQuery};
use crate::web::services::TaskService;

pub fn configure_routes(cfg: &mut web::ServiceConfig) {
    cfg.route("/tasks", web::post().to(create_task))
        .route("/tasks", web::get().to(list_tasks))
        .route("/tasks/{id}", web::get().to(get_task))
        .route("/tasks/{id}/start", web::post().to(start_task))
        .route("/tasks/{id}/stop", web::post().to(stop_task))
        .route("/tasks/{id}/delete", web::delete().to(delete_task));
}

async fn create_task(
    db: web::Data<Database>,
    request: web::Json<CreateTaskRequest>,
) -> Result<HttpResponse> {
    info!("Creating new task: {}", request.name);

    let task_service = TaskService::new(db.get_ref());

    match task_service.create_task(request.into_inner()).await {
        Ok(task) => {
            info!("Task created successfully: {}", task.id);
            Ok(HttpResponse::Created().json(ApiResponse::success(task)))
        }
        Err(e) => {
            error!("Failed to create task: {}", e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(format!(
                    "Failed to create task: {}",
                    e
                ))),
            )
        }
    }
}

async fn list_tasks(db: web::Data<Database>, query: web::Query<TaskQuery>) -> Result<HttpResponse> {
    let task_service = TaskService::new(db.get_ref());

    match task_service.list_tasks(query.into_inner()).await {
        Ok(response) => Ok(HttpResponse::Ok().json(ApiResponse::success(response))),
        Err(e) => {
            error!("Failed to list tasks: {}", e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(format!(
                    "Failed to list tasks: {}",
                    e
                ))),
            )
        }
    }
}

async fn get_task(db: web::Data<Database>, path: web::Path<String>) -> Result<HttpResponse> {
    let task_id = path.into_inner();
    let task_service = TaskService::new(db.get_ref());

    match task_service.get_task(&task_id).await {
        Ok(Some(task)) => Ok(HttpResponse::Ok().json(ApiResponse::success(task))),
        Ok(None) => {
            Ok(HttpResponse::NotFound()
                .json(ApiResponse::<()>::error("Task not found".to_string())))
        }
        Err(e) => {
            error!("Failed to get task {}: {}", task_id, e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(format!(
                    "Failed to get task: {}",
                    e
                ))),
            )
        }
    }
}

async fn start_task(db: web::Data<Database>, path: web::Path<String>) -> Result<HttpResponse> {
    let task_id = path.into_inner();
    let task_service = TaskService::new(db.get_ref());

    info!("Starting task: {}", task_id);

    match task_service.start_task(&task_id).await {
        Ok(task) => {
            info!("Task started successfully: {}", task_id);
            Ok(HttpResponse::Ok().json(ApiResponse::success_with_message(
                task,
                "Task started successfully".to_string(),
            )))
        }
        Err(e) => {
            error!("Failed to start task {}: {}", task_id, e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(format!(
                    "Failed to start task: {}",
                    e
                ))),
            )
        }
    }
}

async fn stop_task(db: web::Data<Database>, path: web::Path<String>) -> Result<HttpResponse> {
    let task_id = path.into_inner();
    let task_service = TaskService::new(db.get_ref());

    info!("Stopping task: {}", task_id);

    match task_service.stop_task(&task_id).await {
        Ok(task) => {
            info!("Task stopped successfully: {}", task_id);
            Ok(HttpResponse::Ok().json(ApiResponse::success_with_message(
                task,
                "Task stopped successfully".to_string(),
            )))
        }
        Err(e) => {
            error!("Failed to stop task {}: {}", task_id, e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(format!(
                    "Failed to stop task: {}",
                    e
                ))),
            )
        }
    }
}

async fn delete_task(db: web::Data<Database>, path: web::Path<String>) -> Result<HttpResponse> {
    let task_id = path.into_inner();
    let task_service = TaskService::new(db.get_ref());

    info!("Deleting task: {}", task_id);

    match task_service.delete_task(&task_id).await {
        Ok(_) => {
            info!("Task deleted successfully: {}", task_id);
            Ok(HttpResponse::Ok().json(ApiResponse::success_with_message(
                (),
                "Task deleted successfully".to_string(),
            )))
        }
        Err(e) => {
            error!("Failed to delete task {}: {}", task_id, e);
            Ok(
                HttpResponse::InternalServerError().json(ApiResponse::<()>::error(format!(
                    "Failed to delete task: {}",
                    e
                ))),
            )
        }
    }
}
