use actix_cors::Cors;
use actix_files::Files;
use actix_web::{middleware::Logger, web, App, HttpServer, Result};
use log::info;
use std::env;

mod common;
mod config;
mod database;
mod handlers;
mod models;
mod services;
mod tools;

use config::AppConfig;
use database::Database;

#[actix_web::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    env_logger::init();

    // 加载配置
    let config = AppConfig::load()?;
    info!(
        "Starting RustScan Web Server on {}:{}",
        config.host, config.port
    );

    // 初始化数据库
    let database = Database::new(&config.database_url).await?;
    database.migrate().await?;

    // 启动HTTP服务器
    HttpServer::new(move || {
        let cors = Cors::default()
            .allow_any_origin()
            .allow_any_method()
            .allow_any_header()
            .max_age(3600);

        App::new()
            .app_data(web::Data::new(database.clone()))
            .wrap(cors)
            .wrap(Logger::default())
            .configure(handlers::configure_routes)
            .service(Files::new("/", "./frontend/dist").index_file("index.html"))
    })
    .bind(format!("{}:{}", config.host, config.port))?
    .run()
    .await?;

    Ok(())
}
